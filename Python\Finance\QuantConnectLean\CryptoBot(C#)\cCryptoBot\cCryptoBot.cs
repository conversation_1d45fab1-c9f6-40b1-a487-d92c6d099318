﻿using Newtonsoft.Json;
using QuantConnect.Brokerages;
using QuantConnect.Data;
using QuantConnect.Data.Consolidators;
using QuantConnect.Data.Market;
using QuantConnect.Indicators;
using QuantConnect.Orders;
using QuantConnect.Orders.Fees;
using QuantConnect.Securities;
using QuantConnect.Securities.CryptoFuture;
using QuantConnect.Util;
using System.Collections.Concurrent;
using System.IO;

namespace QuantConnect.Algorithm.CSharp {
  public class cCryptoBotAlgorithm : QCAlgorithm {
    private string _cryptoSymbol = null!;
    private decimal _mySize = 0.618m; // avoid `Insufficient buying power to complete orders` which might occur because of `adjustment` based on `bid` or `ask`
    private decimal _myFees = 0.00m / 100m;
    private decimal _myLeverage = 0m;
    private decimal _minLadderGap = 0;
    SymbolProperties _symbolProperties = null!;
    private decimal _minimumOrderSize => _symbolProperties.MinimumOrderSize ?? 25m;
    private decimal _longStopLoss = decimal.MaxValue;
    private decimal _longTakeProfit = decimal.MaxValue;
    private decimal _shortStopLoss = decimal.MaxValue;
    private decimal _shortTakeProfit = decimal.MaxValue;
    private int _priceDecimalPlaces = 0;
    private string _priceDecimalFormat = null!;
    private string _positionDecimalFormat = null!;
    private Symbol _qcSymbol = null!;
    private int _barIndex = -1;
    private decimal? _bid;
    private decimal? _ask;
    private decimal _trade;
    private int _openingPosition;
    private Dictionary<string, decimal> _tradingParams = null!;
    private EntryLadderInfo[] _entryLaddering = null!;
    private ExitLadderInfo[] _tpLaddering = null!;
    private ExitLadderInfo[] _slLaddering = null!;
    private ConcurrentQueue<(OrderEvent, Dictionary<string, object>)> _orderEventQueue = new();
    private TickConsolidator _consolidator = null!;
    private RollingWindow<TradeBar> _rollingConsolidatedBar = null!;

    private AverageTrueRange _atr = null!;
    private ExponentialMovingAverage _emaSlow = null!;
    private ExponentialMovingAverage _emaFast = null!;
    private BollingerBands _bbands = null!;
    private RelativeStrengthIndex _rsi = null!;
    private int _backCandles = 7;
    // New state for sequential order processing

    private const string STOP_LOSS_AFTER_TP_REASON = "Protective StopLoss Order placed near the take-profit fill price after order cleanup.";
    private const string POSITION_IS_FULLY_CLOSED_REASON = "Try to cancel all pending Orders, because Position fully closed";
    private OrderState? _currentOrder;
    private OrderState? CurrentOrder {
      get => _currentOrder;
      set {
        _currentOrder = value;
      }
    }
    private bool _waitingForStopLossCancellation = false;
    private HashSet<int> _pendingStopLossCancellations = new HashSet<int>();
    private ExitLadderInfo? _pendingStopLossAfterTP = null;

    private cCryptoBot.Testing _testing = null!;

    private Queue<(string Property, int LadderIndex, int EntryIndex, decimal Quantity, decimal Price, bool IsStopLimit, string Reason)> _pendingOrders { get; } = new Queue<(string, int, int, decimal, decimal, bool, string)>();
    private Queue<(int OrderId, Dictionary<string, object> TagDict, string Reason)> _pendingCancelRequests { get; } = new Queue<(int, Dictionary<string, object>, string)>();

    private class OrderState {
      public OrderTicket Ticket { get; set; } = null!;
      public string Property { get; set; } = null!; // e.g., OpenPosition, StopLossClosePosition
      public int LadderIndex { get; set; } // For SL/TP ladders
      public int EntryIndex { get; set; } // For entry orders
      public DateTime SubmitTime { get; set; }
      public OrderStatus LastStatus { get; set; }
    }

    public override void Initialize() {
      SetTimeZone(TimeZones.Utc);
      var currency = GetParameter("currency", "xDummyError");
      SetAccountCurrency(currency);

      _tradingParams = new Dictionary<string, decimal>
      {
        { "atr_sl_ratio", 1.6m },
        { "atr_tp_ratio", 3.25m },
        { "n_entries", 1m },
        { "num_step_entries", 2m },
        { "sl_levels", 3m },
        { "tp_levels", 10m }
      };

      _entryLaddering = new EntryLadderInfo[(int)_tradingParams["n_entries"]];
      _tpLaddering = new ExitLadderInfo[(int)_tradingParams["tp_levels"]];
      _slLaddering = new ExitLadderInfo[(int)_tradingParams["sl_levels"]];

      var tradeMode = GetParameter("tradeMode", -1); // 0 - SpotMargin, 1 - Futures, 2 - Options

      if (tradeMode != 0 && tradeMode != 1) {
        throw new ArgumentException($"Invalid tradeMode: {tradeMode}. Use 0 for SpotMargin or 1 for Futures.");
      }

      if (tradeMode == 0) {
        SetBrokerageModel(BrokerageName.Binance, AccountType.Margin);
        Log("Trade Mode: SpotMargin - Using Binance Spot/Margin");
      } else if (tradeMode == 1) {
        SetBrokerageModel(BrokerageName.BinanceFutures, AccountType.Margin);
        Log("Trade Mode: Futures - Using Binance Futures");
      }

      _myLeverage = GetParameter("leverage", tradeMode == 0 ? 1m : 3m);
      Log($"Leverage parameter: {_myLeverage}");

      var ticker = GetParameter("symbol", "xDummyError");
      ticker += currency;
      Log($"Ticker parameter: {ticker}");

      Security crypto;
      if (tradeMode == 0) {
        var spotCrypto = AddCrypto(ticker, Resolution.Tick, leverage: _myLeverage);
        crypto = spotCrypto;
        Log($"Added SpotMargin crypto: {crypto.Symbol}, Type: {crypto.Type}, Base currency: {spotCrypto.BaseCurrency?.Symbol ?? "null"}");
      } else {
        var cryptoFuture = AddCryptoFuture(ticker, Resolution.Tick, leverage: _myLeverage);
        crypto = cryptoFuture;

        Log($"Added crypto future: {crypto.Symbol}, Type: {crypto.Type}, Base currency: {cryptoFuture.BaseCurrency?.Symbol ?? "null"}");
      }

      _symbolProperties = crypto.SymbolProperties;

      if (_symbolProperties.MinimumOrderSize == null) {
        Log($"MinimumOrderSize was null, using default value of 25m");
      }

      Log($"Symbol properties - MarketTicker: {_symbolProperties.MarketTicker}, QuoteCurrency: {_symbolProperties.QuoteCurrency}, MinimumPriceVariation: {_symbolProperties.MinimumPriceVariation}, LotSize: {_symbolProperties.LotSize}, MinimumOrderSize: {_minimumOrderSize}");

      _minLadderGap = 3 * _symbolProperties.MinimumPriceVariation;
      Log($"Minimum ladder gap: {_minLadderGap} (3 * {_symbolProperties.MinimumPriceVariation})");

      _cryptoSymbol = _symbolProperties.MarketTicker.Replace(_symbolProperties.QuoteCurrency, "");
      Log($"Extracted crypto symbol: '{_cryptoSymbol}' (from MarketTicker: '{_symbolProperties.MarketTicker}' - QuoteCurrency: '{_symbolProperties.QuoteCurrency}')");

      _priceDecimalPlaces = Math.Max(0, (int)-Math.Log10((double)_symbolProperties.MinimumPriceVariation));
      _priceDecimalFormat = $"F{_priceDecimalPlaces}"; // Creates "F2", "F5"

      var precision = _symbolProperties.LotSize < 1 ? (int)-Math.Log10((double)_symbolProperties.LotSize) : 0;
      _positionDecimalFormat = $"F{precision}"; // Creates "F2", "F5" based on LotSize

      crypto.SetLeverage(_myLeverage);
      if (LiveMode) {
        _qcSymbol = crypto.Symbol;
        SetWarmUp(TimeSpan.FromMinutes(30));
      } else {
        SetStartDate(2025, 3, 4);
        SetEndDate(2025, 3, 6);
        SetCash(_symbolProperties.QuoteCurrency, 100);
        var security = AddData<CustomCryptoData>(ticker, crypto.SymbolProperties, crypto.Exchange.Hours, Resolution.Tick, fillForward: false, leverage: _myLeverage);
        _qcSymbol = security.Symbol;

        security.SetFeeModel(new VillaFeeModel());
        SetBenchmark(ticker);
      }

      _consolidator = new TickConsolidator(TimeSpan.FromMinutes(1));
      _consolidator.DataConsolidated += BarHandler;
      SubscriptionManager.AddConsolidator(_qcSymbol, _consolidator);

      _rollingConsolidatedBar = new RollingWindow<TradeBar>(50);

      _rsi = new RelativeStrengthIndex("RSI(6)", 6, MovingAverageType.Wilders);
      _atr = new AverageTrueRange("ATR(7)", 7, MovingAverageType.Wilders);
      _emaSlow = new ExponentialMovingAverage("EMA(42)", 42);
      _emaFast = new ExponentialMovingAverage("EMA(24)", 24);
      _bbands = new BollingerBands("BB(13,1.1)", 13, 1.1m, MovingAverageType.Simple);

      // Set RollingWindow size for indicators
      _rsi.Window.Size = 50;
      _atr.Window.Size = 50;
      _emaSlow.Window.Size = 50;
      _emaFast.Window.Size = 50;
      _bbands.Window.Size = 50;
      _bbands.UpperBand.Window.Size = 50;
      _bbands.LowerBand.Window.Size = 50;
      _bbands.MiddleBand.Window.Size = 50;

      // Register indicators with consolidator
      RegisterIndicator(_qcSymbol, _rsi, _consolidator);
      RegisterIndicator(_qcSymbol, _atr, _consolidator);
      RegisterIndicator(_qcSymbol, _emaSlow, _consolidator);
      RegisterIndicator(_qcSymbol, _emaFast, _consolidator);
      RegisterIndicator(_qcSymbol, _bbands, _consolidator);

      Schedule.On(DateRules.EveryDay(), TimeRules.Every(TimeSpan.FromMilliseconds(1)), ProcessOrderEventQueue);
      _pendingStopLossAfterTP = null;

      _testing = new cCryptoBot.Testing(this, _qcSymbol, _symbolProperties);
    }

    public override void OnData(Slice slice) {
      if (!slice.Ticks.ContainsKey(_qcSymbol)) return;

      foreach (var tick in slice.Ticks[_qcSymbol]) {
        if (tick.TickType == TickType.Trade) {
          _trade = tick.LastPrice;
          // Log($"xxx C# barIdx={_barIndex}, {tick.Time:yyyy-MM-dd HH:mm:ss.fff}, {_trade}, {tick.Quantity}");
        } else if (tick.TickType == TickType.Quote) {
          _bid = tick.BidPrice;
          _ask = tick.AskPrice;
        }
      }
      if (!LiveMode) {
        _bid = _trade;
        _ask = _trade;
      }
    }

    public override void OnWarmupFinished() {
      var position = Portfolio[_qcSymbol].Quantity;
      var openOrders = Transactions.GetOpenOrders(_qcSymbol);

      if (Math.Abs(position) >= 1e-5m)
        throw new Exception($"Warmup finished with non-zero position: {position:F5}");
      if (openOrders.Count > 0)
        throw new Exception($"Warmup finished with {openOrders.Count} open order(s): {string.Join(", ", openOrders.Select(o => o.ToString()))}");
      var cryptoBalance = Portfolio.CashBook[_cryptoSymbol].Amount;
      if (Math.Abs(cryptoBalance) >= 1e-8m)
        throw new Exception($"Warming up finished with non-zero CASH balance: {cryptoBalance}");
      Log($"on_warmup_finished - Portfolio: {Portfolio[_qcSymbol]}, No open orders confirmed.");

      _testing?.PlaceTestOrders();
      _testing?.ScheduleCancelAllOrdersAfter15Minutes();
    }

    public override void OnOrderEvent(OrderEvent orderEvent) {
      var ticket = orderEvent.Ticket;
      var rawTag = ticket.Tag;
      Dictionary<string, object> tagDict;

      try {
        var jsonStart = rawTag.IndexOf('{');
        var jsonEnd = rawTag.LastIndexOf('}') + 1;
        var jsonStr = (jsonStart != -1 && jsonEnd != 0 && jsonEnd > jsonStart)
                        ? rawTag.Substring(jsonStart, jsonEnd - jsonStart)
                        : rawTag;
        tagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonStr)
                  ?? new Dictionary<string, object>();
      } catch (Exception ex) {
        Log($"Error deserializing tag '{rawTag}' for order {orderEvent.OrderId}: {ex.Message}");
        tagDict = new Dictionary<string, object>();
      }

      DateTime submitTime;
      if (tagDict.TryGetValue("SubmitTime", out var submitTimeObj) &&
          submitTimeObj is string submitTimeStr &&
          DateTime.TryParse(submitTimeStr, out submitTime)) {
      } else {
        submitTime = DateTime.UtcNow;
        Log($"Could not parse SubmitTime from tag for order {orderEvent.OrderId}. Using UtcNow.");
      }

      var curUtc = DateTime.UtcNow;
      var timeDiff = (curUtc - submitTime).TotalSeconds;

      var cryptoBalance = Portfolio.CashBook[_cryptoSymbol].Amount;
      var position = Portfolio[_qcSymbol].Quantity;
      var order = Transactions.GetOrderById(orderEvent.OrderId);

      var logPrefix = $"barIdx={_barIndex}, crypto={cryptoBalance}, position={position}, TimeDiff({orderEvent.Status})={timeDiff:F3}s, " +
                      $"QTY={orderEvent.Quantity}, OID={orderEvent.OrderId}, " +
                      $"{{bid={_bid!.Value.ToString(_priceDecimalFormat)},ask={_ask!.Value.ToString(_priceDecimalFormat)}}}, " + 
                      $"BID={string.Join(",", order.BrokerId)}, stopPrice={orderEvent.StopPrice}, limitPrice={orderEvent.LimitPrice}, fillPrice={orderEvent.FillPrice:F5}, property={tagDict["property"]}";

      bool flag = (orderEvent.Status == OrderStatus.Filled && (tagDict["property"].ToString() == "StopLossClosePosition" || tagDict["property"].ToString() == "TakeProfitClosePosition"));
      if (!flag) {
        Log(logPrefix);
      } else {
        var latestProfitLoss = 0m;
        var transactionRecord = Transactions.TransactionRecord;
        if (transactionRecord.Any()) {
          latestProfitLoss = transactionRecord.OrderByDescending(x => x.Key).First().Value;
        }

        var reason = tagDict.TryGetValue("Reason", out var reasonObj) && reasonObj != null ? reasonObj.ToString() : null;
        if (!string.IsNullOrEmpty(reason)) {
          Log($"{logPrefix}, OrderFee={orderEvent.OrderFee.ToString()}, ProfitLoss={latestProfitLoss:F5}, Reason=`{reason}`");
        } else {
          Log($"{logPrefix}, OrderFee={orderEvent.OrderFee.ToString()}, ProfitLoss={latestProfitLoss:F5}");
        }
      }

      tagDict["enqueue_time"] = GetUtcTime();
      _orderEventQueue.Enqueue((orderEvent, tagDict));
    }

    private void PlaceProtectiveOrders(string reason) {
      var position = Portfolio[_qcSymbol].Quantity;
      var adjustment = _symbolProperties.MinimumPriceVariation * 3;
      var goLong = position > 0;

      decimal stopLossPrice = goLong ? _bid!.Value - adjustment : _ask!.Value + adjustment;
      decimal takeProfitPrice = goLong ? _ask!.Value + adjustment : _bid!.Value - adjustment;


      _pendingOrders.Enqueue(("StopLossClosePosition", 0, 0, -position, stopLossPrice, true, reason));
      _pendingOrders.Enqueue(("TakeProfitClosePosition", 0, 0, -position, takeProfitPrice, false, reason));

      Log($"Enqueued emergency protection orders: SL={stopLossPrice.ToString(_priceDecimalFormat)}, TP={takeProfitPrice.ToString(_priceDecimalFormat)}, QTY={(-position).ToString(_positionDecimalFormat)}");
    }

    private void ProcessOrderEventQueue() {
      var bufferedEvents = new List<(OrderEvent, Dictionary<string, object>)>();

      while (_orderEventQueue.TryDequeue(out var queueItem)) {
        var (orderEvent, tagDict) = queueItem;

        if (CurrentOrder != null && orderEvent.Ticket.OrderId != CurrentOrder.Ticket.OrderId) {
          bufferedEvents.Add((orderEvent, tagDict));
          continue;
        }

        if (!tagDict.TryGetValue("property", out var propertyObj) || !(propertyObj is string orderProperty)) {
          Log($"Order event {orderEvent.OrderId} missing 'property' in tag. Skipping.");
          continue;
        }

        var position = Portfolio[_qcSymbol].Quantity;
        if (CurrentOrder != null)
          CurrentOrder.LastStatus = orderEvent.Status;

        DateTime enqueueTime;
        if (tagDict.TryGetValue("enqueue_time", out var enqueueTimeObj) &&
            enqueueTimeObj is string enqueueTimeStr &&
            DateTime.TryParse(enqueueTimeStr, out enqueueTime)) {
          var dequeueTime = DateTime.UtcNow;
          var queueTimeMs = (dequeueTime - enqueueTime).TotalMilliseconds;
          var currentOrderInfo = CurrentOrder != null ? $" (CurrentOrder: OID={CurrentOrder.Ticket.OrderId})" : "";
          Log($"Order event processed: QueueCost={queueTimeMs:F2}ms, Property={orderProperty}, `{orderEvent.ShortToString()}`{currentOrderInfo}");
        } else {
          Log($"Could not parse enqueue_time from tag for order {orderEvent.ShortToString()}");
        }

        if (orderEvent.Status == OrderStatus.Invalid) {
          HandleInvalidOrder(orderEvent, tagDict, orderProperty);
          continue; // Continue processing other events
        }

        if (orderEvent.Status == OrderStatus.Filled) {
          if (orderProperty == "OpenPosition") {
            HandleOpenPosition(orderEvent, tagDict);
          } else if (orderProperty == "StopLossClosePosition" || orderProperty == "TakeProfitClosePosition") {
            HandleExitPosition(orderEvent, tagDict, orderProperty);
          }
          if (position == 0) {
            EnqueueCancelAllOrders(POSITION_IS_FULLY_CLOSED_REASON);
            _openingPosition = 0;
            ResetLadders();
            _pendingOrders.Clear();
            _waitingForStopLossCancellation = false;
            _pendingStopLossCancellations.Clear();
            CurrentOrder = null;
          } else {
            Log($"Clearing CurrentOrder after handling {orderProperty} (OID={CurrentOrder?.Ticket.OrderId})");
            CurrentOrder = null;
          }
          continue; // Continue processing other events
        }

        if (orderEvent.Status == OrderStatus.PartiallyFilled) {
          // Ignore partial fills, wait for full fill or cancellation
          continue; // Continue processing other events
        }

        if (orderEvent.Status == OrderStatus.Canceled) {
          if (orderProperty == "StopLossClosePosition" && _pendingStopLossCancellations.Contains(orderEvent.OrderId)) {
            _pendingStopLossCancellations.Remove(orderEvent.OrderId);
            Log($"StopLoss order {orderEvent.OrderId} cancellation confirmed. Pending cancellations remaining: {_pendingStopLossCancellations.Count}");
            if (_pendingStopLossCancellations.Count == 0) {
              _waitingForStopLossCancellation = false;
              Log("All StopLoss cancellations completed. Ready to place new StopLoss order.");

              // Place the stored StopLoss order if one exists
              if (_pendingStopLossAfterTP.HasValue) {
                var slOrder = _pendingStopLossAfterTP.Value;
                _pendingOrders.Enqueue(("StopLossClosePosition", 0, 0, slOrder.Amount, slOrder.Price, true, STOP_LOSS_AFTER_TP_REASON));
                Log($"Enqueuing stored StopLoss order at price {slOrder.Price} after cancellations completed.");
                _pendingStopLossAfterTP = null;
              }
            }
          }
          CurrentOrder = null;
          continue; // Continue processing other events
        }

        if (orderEvent.Status == OrderStatus.Submitted) {
          CurrentOrder = null;
          continue; // Continue processing other events
        }
      }

      foreach (var bufferedEvent in bufferedEvents) {
        _orderEventQueue.Enqueue(bufferedEvent);
      }

      while (_pendingCancelRequests.Count > 0 && CurrentOrder == null) {
        var (orderId, tagDict, reason) = _pendingCancelRequests.Dequeue();
        var order = Transactions.GetOrderById(orderId);

        if (order.Status == OrderStatus.PartiallyFilled || order.Status == OrderStatus.Filled) {
          Log($"Order {orderId} is {order.Status}. Skipping cancellation and waiting for completion: {reason}");
          continue;
        }

        if (order.Status == OrderStatus.New) {
          Log($"Order {orderId} is in New status. Requeueing cancellation: {reason}");
          _pendingCancelRequests.Enqueue((orderId, tagDict, reason));
          continue;
        }

        var ticket = Transactions.GetOrderTicket(orderId);
        var cancelResult = ticket.Cancel(JsonConvert.SerializeObject(tagDict));
        if (!cancelResult.IsError) {
          Log($"Cancellation submitted for order {orderId}: {reason}");
          if (tagDict.TryGetValue("property", out var propObj) && propObj?.ToString() == "StopLossClosePosition") {
            _pendingStopLossCancellations.Add(orderId);
          }
        }
      }

      while (CurrentOrder == null && !_waitingForStopLossCancellation && _pendingOrders.Count > 0) {
        var (property, ladderInx, entryInx, quantity, price, isStopLimit, reason) = _pendingOrders.Dequeue();
        PlaceNextOrder(reason, property, ladderInx, entryInx, quantity, price, isStopLimit);
      }
    }

    private void HandleInvalidOrder(OrderEvent orderEvent, Dictionary<string, object> tagDict, string orderProperty) {
      _openingPosition = 0;
      var position = Portfolio[_qcSymbol].Quantity;

      if (position == 0) {
        Log("No position to adjust, invalid order ignored");
        CurrentOrder = null;
        return;
      }

      var retryWithOriginalParamsCodes = new HashSet<int> { -1021, -3006, -3007, -3044, -3045 };
      bool retryWithOriginalParams = false;
      int? errorCode = null;

      try {
        var messageData = JsonConvert.DeserializeObject<Dictionary<string, object>>(orderEvent.Message);
        if (messageData != null) {
          if (messageData.TryGetValue("code", out var codeObj) && codeObj != null) {
            string? codeStr = codeObj.ToString();
            if (codeStr != null && int.TryParse(codeStr, out var code)) {
              errorCode = code;
              retryWithOriginalParams = retryWithOriginalParamsCodes.Contains(code);
            }
          }
        } else {
          Log($"Deserialized message data is null for message: {orderEvent.Message}");
        }
      } catch (JsonException) {
        Log($"Non-JSON error message received: `{orderEvent.Message}`, Order rejected by Binance");
      } catch (Exception ex) {
        Log($"Error processing error code from message: {orderEvent.Message}. Error: {ex.Message}");
      }

      var adjustment = _symbolProperties.MinimumPriceVariation * 2;
      var quantity = retryWithOriginalParams ? orderEvent.Quantity : -position;

      if (orderProperty == "StopLossClosePosition") {
        if (retryWithOriginalParams && orderEvent.LimitPrice.HasValue && orderEvent.StopPrice.HasValue) {
          var limitPrice = orderEvent.LimitPrice.Value;
          tagDict["Reason"] = $"Retrying StopLoss with original params, Error code: {errorCode ?? 0}";

          _pendingOrders.Enqueue((orderProperty, Convert.ToInt32(tagDict["LadderInx"]), 0, quantity, limitPrice, true, tagDict["Reason"]?.ToString() ?? "StopLoss retry"));

          Log($"Enqueued StopLoss LimitOrder retry barIdx={_barIndex}, QTY={quantity}, LimitPrice={limitPrice}, Reason=`{tagDict["Reason"]}`");
        } else {
          EnqueueCancelOrdersWithProperty("StopLossClosePosition", "Adjusting StopLoss ladder");

          var goLong = position > 0;
          decimal stopLossPrice = goLong ? _bid!.Value - adjustment : _ask!.Value + adjustment;

          _pendingOrders.Enqueue((orderProperty, 0, 0, -position, stopLossPrice, true, tagDict["Reason"]?.ToString() ?? "StopLoss retry"));
          Log($"Enqueued StopLoss LimitOrder retry barIdx={_barIndex}, QTY={quantity}, stopLossPrice={stopLossPrice}, Reason=`{tagDict["Reason"]}`");
        }
      } else if (orderProperty == "TakeProfitClosePosition") {
        if (retryWithOriginalParams && orderEvent.LimitPrice.HasValue) {
          var limitPrice = orderEvent.LimitPrice.Value;
          tagDict["Reason"] = $"Retrying TakeProfit with original params, Error code: {errorCode ?? 0}";

          _pendingOrders.Enqueue((orderProperty, Convert.ToInt32(tagDict["LadderInx"]), 0, quantity, limitPrice, false, tagDict["Reason"]?.ToString() ?? "TakeProfit retry"));

          Log($"Enqueued TakeProfit LimitOrder retry barIdx={_barIndex}, QTY={quantity}, LimitPrice={limitPrice}, Reason=`{tagDict["Reason"]}`");
        } else {
          EnqueueCancelOrdersWithProperty("TakeProfitClosePosition", "Adjusting TakeProfit ladder");
          var limitPrice = position > 0 ? _bid!.Value + adjustment : _ask!.Value - adjustment;
          tagDict["Reason"] = $"Invalid TakeProfit order retry (adjusted params), Error code: {errorCode ?? 0}";

          _pendingOrders.Enqueue((orderProperty, Convert.ToInt32(tagDict["LadderInx"]), 0, quantity, limitPrice, false, tagDict["Reason"]?.ToString() ?? "TakeProfit retry"));

          Log($"Enqueued TakeProfit LimitOrder retry barIdx={_barIndex}, QTY={quantity}, LimitPrice={limitPrice}, Reason=`{tagDict["Reason"]}`");
        }
      }

      CurrentOrder = null;
    }

    private void HandleOpenPosition(OrderEvent orderEvent, Dictionary<string, object> tagDict) {
      var position = Portfolio[_qcSymbol].Quantity;
      var nextSize = Convert.ToDecimal(tagDict["NextOrderSize"]);
      var initPosition = Convert.ToDecimal(tagDict["InitPosition"]);
      var entryPrice = orderEvent.FillPrice;

      var barIdx = Convert.ToInt32(tagDict["barIdx"]);
      var entryIdx = Array.FindIndex(_entryLaddering, e => e.BarIndex == barIdx);
      _entryLaddering[entryIdx].PositionChanged = Math.Abs(position - initPosition);

      EnqueueCancelAllOrders("Setting up new position ladders");

      _slLaddering = PopulateLaddering(
          -position,
          entryPrice,
          position > 0 ? _longStopLoss : _shortStopLoss,
          (int)_tradingParams["sl_levels"],
          _slLaddering,
          true
      );

      _tpLaddering = PopulateLaddering(
          -position,
          entryPrice,
          position > 0 ? _longTakeProfit : _shortTakeProfit,
          (int)_tradingParams["tp_levels"],
          _tpLaddering,
          false
      );

      for (int i = 0; i < _slLaddering.Length; i++)
        _pendingOrders.Enqueue(("StopLossClosePosition", i, 0, _slLaddering[i].Amount, _slLaddering[i].Price, true, "Initial StopLoss ladder setup"));
      for (int i = 0; i < _tpLaddering.Length; i++)
        _pendingOrders.Enqueue(("TakeProfitClosePosition", i, 0, _tpLaddering[i].Amount, _tpLaddering[i].Price, false, "Initial TakeProfit ladder setup"));

      CurrentOrder = null;
    }

    private void HandleExitPosition(OrderEvent orderEvent, Dictionary<string, object> tagDict, string orderProperty) {
      var ladderInx = Convert.ToInt32(tagDict["LadderInx"]);
      var position = Portfolio[_qcSymbol].Quantity;
      var ladder = orderProperty == "StopLossClosePosition" ? _slLaddering : _tpLaddering;

      _openingPosition = 0;
      var remainingPosition = Math.Abs(position);

      if (remainingPosition == 0) {
        Log("Position fully closed, exiting HandleExitPosition");
        return;
      }

      if (orderProperty == "TakeProfitClosePosition") {
        var openOrders = Transactions.GetOpenOrders();
        _pendingStopLossCancellations.Clear();
        foreach (var order in openOrders) {
          try {
            var orderTagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(order.Tag) ?? new Dictionary<string, object>();

            if (orderTagDict.TryGetValue("property", out var propObj) && propObj?.ToString() == "StopLossClosePosition") {
              orderTagDict["Reason"] = "Take-profit triggered, canceling all stop-loss orders";
              _pendingCancelRequests.Enqueue((order.Id, orderTagDict, orderTagDict["Reason"]?.ToString() ?? "Take-profit triggered"));
              _pendingStopLossCancellations.Add(order.Id);
            }
          } catch (Exception ex) {
            Log($"Error deserializing tag '{order.Tag}' for order {order.Id} during HandleExitPosition: {ex.Message}");
          }
        }

        if (_pendingStopLossCancellations.Count > 0) {
          _waitingForStopLossCancellation = true;
          Log($"Initiated cancellation of {_pendingStopLossCancellations.Count} StopLoss orders. Waiting for confirmation before placing new StopLoss.");
        } else {
          _waitingForStopLossCancellation = false;
          Log("No StopLoss orders found to cancel. Proceeding to set new StopLoss.");
        }

        // Reset StopLoss ladder with a single order at the TakeProfit fill price
        _slLaddering = new ExitLadderInfo[1];
        _slLaddering[0] = new ExitLadderInfo { Price = orderEvent.FillPrice, Amount = -position };
        Log($"Prepared new StopLoss order at price {orderEvent.FillPrice} after TakeProfit fill, remaining position: {position}");

        if (_waitingForStopLossCancellation) {
          // Store the pending StopLoss order for after cancellations complete
          _pendingStopLossAfterTP = _slLaddering[0];
        } else {
          var nonStopLossOrders = _pendingOrders.Where(order => order.Property != "StopLossClosePosition").ToList();
          _pendingOrders.Clear();
          foreach (var order in nonStopLossOrders) {
            _pendingOrders.Enqueue(order);
          }
          _pendingOrders.Enqueue(("StopLossClosePosition", 0, 0, _slLaddering[0].Amount, _slLaddering[0].Price, true, "StopLoss after TakeProfit fill"));
          Log($"Enqueued new StopLossClosePosition order at price {_slLaddering[0].Price}, quantity {_slLaddering[0].Amount}");
        }

        var tpUnfilled = new List<(decimal Price, decimal Amount)>();

        foreach (var order in openOrders) {
          var orderTagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(order.Tag) ?? new Dictionary<string, object>();
          if (orderTagDict.TryGetValue("property", out var propObj) && propObj?.ToString() == "TakeProfitClosePosition") {
            decimal price = order.Type == OrderType.Limit ? ((LimitOrder)order).LimitPrice : 0;
            decimal amount = Math.Abs(order.Quantity);
            tpUnfilled.Add((price, amount));
          }
        }

        var reason = tagDict.TryGetValue("Reason", out var reasonObj) && reasonObj != null ? reasonObj.ToString() : null;
        if (tpUnfilled.Count > 0) {
          decimal totalTpUnfilledAmount = tpUnfilled.Sum(tp => tp.Amount);
          if (remainingPosition < totalTpUnfilledAmount) {
            Log($"Adjusting TakeProfit ladder due to smaller remaining position: {remainingPosition} vs unfilled TP: {totalTpUnfilledAmount}");
            EnqueueCancelOrdersWithProperty("TakeProfitClosePosition", "Adjusting TakeProfit ladder for remaining position");

            // Adjust the last few orders to match remaining position and ensure minimum order value
            decimal remainingToClose = remainingPosition;
            var newTpLaddering = new List<ExitLadderInfo>();
            decimal minOrderSize = (decimal)(_minimumOrderSize / _trade);

            for (int i = 0; i < tpUnfilled.Count && remainingToClose > 0; i++) {
              var currentStep = tpUnfilled[i];
              decimal orderSize = Math.Min(currentStep.Amount, remainingToClose);
              if (orderSize * _trade < _minimumOrderSize && i == tpUnfilled.Count - 1) {
                // Last order, need to merge with previous if possible
                if (newTpLaddering.Count > 0) {
                  var lastAdded = newTpLaddering[newTpLaddering.Count - 1];
                  newTpLaddering[newTpLaddering.Count - 1] = new ExitLadderInfo {
                    Price = lastAdded.Price,
                    Amount = lastAdded.Amount + (position > 0 ? -orderSize : orderSize),
                  };
                  remainingToClose -= orderSize;
                }
              } else if (orderSize * _trade >= _minimumOrderSize) {
                newTpLaddering.Add(new ExitLadderInfo {
                  Price = currentStep.Price,
                  Amount = position > 0 ? -orderSize : orderSize,
                });
                remainingToClose -= orderSize;
              }
            }

            _tpLaddering = newTpLaddering.ToArray();
            for (int i = 0; i < _tpLaddering.Length; i++)
              _pendingOrders.Enqueue(("TakeProfitClosePosition", i, 0, _tpLaddering[i].Amount, _tpLaddering[i].Price, false, "Adjusted TakeProfit ladder"));
          }
        }
      }
    }

    private void PlaceNextOrder(string reason, string property, int ladderInx, int entryInx, decimal quantity, decimal price, bool isStopLimit) {
      var currentPosition = Portfolio[_qcSymbol].Quantity;

      // The order that was intended to be canceled might have been filled before the cancellation succeeded, so retrieve the latest position quantity before placing a new order.
      if (!_testing.IsTesting && (property == "StopLossClosePosition" || property == "TakeProfitClosePosition")) {
        if (currentPosition == 0) {
          Log($"Skipping {property} order, QTY={quantity}, ladderInx={ladderInx}, entryInx={entryInx}, reason={reason}, no position to close. Current position: {currentPosition}");
          CurrentOrder = null;
          return;
        }

        var maxCloseQuantity = Math.Min(Math.Abs(quantity), Math.Abs(currentPosition));
        var adjustedQuantity = currentPosition > 0 ? -maxCloseQuantity : maxCloseQuantity;
        if (Math.Abs(adjustedQuantity) < Math.Abs(quantity)) {
          Log($"Adjusted {property} order quantity from {quantity} to {adjustedQuantity} based on current position {currentPosition}");
          quantity = adjustedQuantity;
        }

        if (Math.Abs(quantity * price) < _minimumOrderSize) {
          Log($"Skipping {property} order - adjusted quantity {quantity} * price {price} = {Math.Abs(quantity * price)} below minimum order size {_minimumOrderSize}");
          CurrentOrder = null;
          return;
        }
      }

      var tagDict = new Dictionary<string, object>
      {
        { "barIdx", _barIndex },
        { "LadderInx", ladderInx },
        { "property", property },
        { "SubmitTime", GetUtcTime() },
        { "Reason", reason },
      };

      if (property == "OpenPosition") {
        tagDict["entryIdx"] = entryInx;
        tagDict["NextOrderSize"] = Math.Abs(quantity);
        tagDict["InitPosition"] = Portfolio[_qcSymbol].Quantity;
        tagDict["entryPrice"] = price;
      }

      OrderTicket ticket;
      if (isStopLimit) {
        var limitPrice = price;
        var position = Portfolio[_qcSymbol].Quantity;

        var stopMultiplier = 1;

        var stopGap = stopMultiplier * _symbolProperties.MinimumPriceVariation;
        var stopPrice = position > 0 ? limitPrice - stopGap : limitPrice + stopGap;

        ticket = StopLimitOrder(_qcSymbol, quantity, stopPrice, limitPrice, JsonConvert.SerializeObject(tagDict));
      } else {
        var orderProperties = new BinanceOrderProperties { PostOnly = true };
        ticket = LimitOrder(_qcSymbol, quantity, price, JsonConvert.SerializeObject(tagDict), orderProperties);
      }

      CurrentOrder = new OrderState {
        Ticket = ticket,
        Property = property,
        LadderIndex = ladderInx,
        EntryIndex = entryInx,
        SubmitTime = DateTime.UtcNow,
        LastStatus = ticket.Status
      };

      if (property == "OpenPosition") {
        Log($"barIdx={_barIndex}, entryIdx={entryInx}, OpenPosition, OID={ticket.OrderId}, Status={ticket.Status}, QTY={ticket.Quantity}, Tag=`{ticket.Tag}` [SET AS CurrentOrder]");
      } else {
        Log($"barIdx={_barIndex}, OID={ticket.OrderId}, Status={ticket.Status}, QTY={ticket.Quantity}, Tag=`{ticket.Tag}` [SET AS CurrentOrder]");
      }
    }

    private void EnqueueCancelAllOrders(string reason) {
      var openOrders = Transactions.GetOpenOrders();
      foreach (var order in openOrders) {
        Dictionary<string, object> orderTagDict;
        try {
          orderTagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(order.Tag)
                          ?? new Dictionary<string, object>();
        } catch (Exception ex) {
          Log($"Error deserializing tag '{order.Tag}' for order {order.Id} during EnqueueCancelAllOrders: {ex.Message}");
          orderTagDict = new Dictionary<string, object>();
        }

        orderTagDict["Reason"] = reason;
        _pendingCancelRequests.Enqueue((order.Id, orderTagDict, reason));
      }
      if (openOrders.Count > 0)
        Log($"Enqueued cancellation for {openOrders.Count} orders: {reason}");
    }

    private void ResetLadders() {
      _tpLaddering = new ExitLadderInfo[(int)_tradingParams["tp_levels"]];
      _slLaddering = new ExitLadderInfo[(int)_tradingParams["sl_levels"]];
      _longStopLoss = _longTakeProfit = decimal.MaxValue;
      _shortStopLoss = _shortTakeProfit = decimal.MaxValue;
      _pendingStopLossAfterTP = null;
    }

    private bool PrecheckLaddering(decimal startPrice, decimal endPrice, int nLevels) {
      if (nLevels <= 0) return false;

      if (Math.Abs(startPrice - endPrice) < 1e-9m && nLevels > 1) {
        Log($"Precheck laddering failed: start_price ({startPrice.ToString(_priceDecimalFormat)}) and end_price ({endPrice.ToString(_priceDecimalFormat)}) too close for {nLevels} levels");
        return false;
      }

      var diffUnit = (endPrice - startPrice) / nLevels;
      var firstLevelPrice = startPrice + diffUnit;
      var exponent = Math.Max(0, (int)-Math.Log10((double)_symbolProperties.MinimumPriceVariation));
      firstLevelPrice = Math.Round(firstLevelPrice, exponent);

      var actualGap = Math.Abs(firstLevelPrice - startPrice);
      var isValid = actualGap >= _minLadderGap;

      Log($"Precheck laddering {(isValid ? "Passed" : "Failed")}: actual_gap ({actualGap.ToString(_priceDecimalFormat)}) {(isValid ? ">=" : "<")} required_gap ({_minLadderGap.ToString(_priceDecimalFormat)}), start_price={startPrice.ToString(_priceDecimalFormat)}, end_price={endPrice.ToString(_priceDecimalFormat)}, n_levels={nLevels}");
      return isValid;
    }

    private ExitLadderInfo[] PopulateLaddering(decimal position, decimal startPrice, decimal endPrice, int nLevels, ExitLadderInfo[] laddering, bool isStopLoss) {
      if (position == 0 || startPrice <= 0 || endPrice <= 0 || Math.Abs(startPrice - endPrice) / nLevels <= _symbolProperties.MinimumPriceVariation)
        throw new Exception("Invalid laddering parameters");

      var granularityCnt = Math.Abs(position) / _symbolProperties.LotSize;
      if (granularityCnt < 0.00004m)
        throw new Exception("Position too small for granularity");

      var pictLaddering = GetIntervalLists(nLevels);
      var newLaddering = new ExitLadderInfo[nLevels];
      var diffUnit = (endPrice - startPrice) / nLevels;

      if (Math.Abs(diffUnit) <= _symbolProperties.MinimumPriceVariation)
        throw new Exception($"Diff unit ({diffUnit}) too small compared to laddering_diff ({_symbolProperties.MinimumPriceVariation})");

      decimal totalAssigned = 0;
      var precision = _symbolProperties.LotSize < 1 ? (int)-Math.Log10((double)_symbolProperties.LotSize) : 0;
      var exponent = Math.Max(0, (int)-Math.Log10((double)_symbolProperties.MinimumPriceVariation));

      for (int inx = 0; inx < nLevels; inx++) {
        var ladderInxPrice = startPrice + (inx + 1) * diffUnit;
        newLaddering[inx].Price = Math.Round(ladderInxPrice, exponent);

        decimal orderSize;
        if (inx < nLevels - 1)
          orderSize = Math.Round(pictLaddering[inx] / 100 * position, precision);
        else
          orderSize = Math.Round(position - totalAssigned, precision);

        totalAssigned += orderSize;
        newLaddering[inx].Amount = orderSize;

        var orderValue = Math.Abs(newLaddering[inx].Price * newLaddering[inx].Amount);
        if (orderValue <= _minimumOrderSize)
          throw new Exception($"Order value below minimum: {orderValue.ToString(_priceDecimalFormat)} <= {_minimumOrderSize:F0} (Price: {newLaddering[inx].Price.ToString(_priceDecimalFormat)}, Amount: {newLaddering[inx].Amount.ToString(_positionDecimalFormat)}, Ladder Index: {inx})");
      }

      return newLaddering;
    }

    private List<decimal> GetIntervalLists(decimal numParts) {
      var result = new List<decimal>();
      for (decimal i = 0m; i < numParts; i++)
        result.Add(100m / numParts);
      return result;
    }

    private void LogVbt(DateTime tic, TradeBar bar, string barStartTime, int signalValue) {
      var costSeconds = (DateTime.UtcNow - tic).TotalSeconds;
      var vbtString = $"`{barStartTime}, {bar.Open.ToString(_priceDecimalFormat)}, {bar.High.ToString(_priceDecimalFormat)}, {bar.Low.ToString(_priceDecimalFormat)}, {bar.Close.ToString(_priceDecimalFormat)}, {bar.Volume:F1}, {signalValue}`";
      var position = Portfolio[_qcSymbol].Quantity;
      var cashBalance = Portfolio.CashBook[_symbolProperties.QuoteCurrency].Amount;
      var cryptoBalance = Portfolio.CashBook[_cryptoSymbol].Amount;
      Log($"barIdx={_barIndex}, opening={_openingPosition}, position={position.ToString(_positionDecimalFormat)}, pendingOrders={Transactions.GetOpenOrders(_qcSymbol).Count}, {_symbolProperties.QuoteCurrency}={cashBalance:F2}, crypto={cryptoBalance:F2}, cost={costSeconds:F3}s, vbt: {vbtString}");
    }

    private void HandleEmergencyStopLossRefresh(TradeBar bar, List<Order> openOrders, decimal position) {
      if (CurrentOrder != null) {
        Log($"Skipping emergency SL refresh: Active order in progress. (CurrentOrder: OID={CurrentOrder.Ticket.OrderId})");
        return;
      }

      var stopLossOrders = new List<(Order, Dictionary<string, object>)>();
      foreach (var loopOrder in openOrders) {
        try {
          var orderTagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(loopOrder.Tag);
          if (orderTagDict != null && orderTagDict.ContainsKey("property") && orderTagDict["property"].ToString() == "StopLossClosePosition")
            stopLossOrders.Add((loopOrder, orderTagDict));
        } catch (Exception ex) {
          Log($"Error decoding tag for order {loopOrder.Id}: '{loopOrder.Tag}'. Error: {ex.Message}");
        }
      }

      if (stopLossOrders.Count != 1) { // only when there is one and only one StopLossClosePosition Open Order shall we handle it
        return;
      }

      var order = stopLossOrders[stopLossOrders.Count - 1].Item1;
      var tagDict = stopLossOrders[stopLossOrders.Count - 1].Item2;
      //if (tagDict.TryGetValue("bar_inx", out var barInxObj) && int.TryParse(barInxObj?.ToString(), out int barInx) && _barIndex - barInx < 50) {
      //  return;
      //}

      var stopLimitOrder = Transactions.GetOrderById(order.Id) as StopLimitOrder;
      if (stopLimitOrder == null) {
        Log($"Order {order.Id} is not a StopLimitOrder. Skipping refresh check.");
        return;
      }

      var stopPrice = stopLimitOrder.StopPrice;
      var limitPrice = stopLimitOrder.LimitPrice;

      bool priceTooFar =
          // (position < 0 && (bar.Close < limitPrice - _atr.Current.Value /*|| bar.Close > limitPrice + _atr.Current.Value*/)) ||
          (position > 0 && (bar.Close > limitPrice + _atr.Current.Value /*|| bar.Close < limitPrice - _atr.Current.Value*/));

      if (priceTooFar) {
        Log($"Detected potentially stale StopLoss order {order.Id} at barIdx {_barIndex}. Bid={_bid!.Value.ToString(_priceDecimalFormat)}, Ask={_ask!.Value}, LimitPrice: {limitPrice.ToString(_priceDecimalFormat)}, Position: {position}");

        tagDict["Reason"] = $"Canceling stale StopLoss for refresh at barIdx {_barIndex}";
        _pendingCancelRequests.Enqueue((order.Id, tagDict, tagDict["Reason"].ToString()!));

        EnqueueCancelAllOrders("priceTooFar, clearing all stale orders to place new protective TakeProfit/StopLoss Orders");
        PlaceProtectiveOrders($"priceTooFar at barIdx {_barIndex}");
      }
    }

    private void BarHandler(object? sender, TradeBar bar) {
      var tic = DateTime.UtcNow;
      _barIndex++;
      _rollingConsolidatedBar.Add(bar);

      var barStartTime = bar.Time.ToString("yyyy-MM-dd HH:mm:ss");

      var openOrders = Transactions.GetOpenOrders(_qcSymbol);
      foreach (var order in openOrders) {
        try {
          var tagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(order.Tag) ?? new Dictionary<string, object>();

          if (tagDict.TryGetValue("property", out var propObj) && propObj?.ToString() == "OpenPosition" &&
              tagDict.TryGetValue("barIdx", out var barInxObj) && int.TryParse(barInxObj?.ToString(), out int orderBarInx) &&
              _barIndex - orderBarInx > 0) {
            tagDict["Reason"] = $"Unfilled OpenPosition order from barIdx {orderBarInx} canceled at barIdx {_barIndex}";
            _pendingCancelRequests.Enqueue((order.Id, tagDict, tagDict["Reason"]?.ToString() ?? "Unfilled OpenPosition order canceled"));
            _openingPosition = 0;
            Log(tagDict["Reason"].ToString());
          }
        } catch (Exception ex) {
          Log($"Error deserializing tag '{order.Tag}' for order {order.Id} during BarHandler open order check: {ex.Message}");
        }
      }

      var position = Portfolio[_qcSymbol].Quantity;
      if (position != 0 && openOrders.Count > 0)
        HandleEmergencyStopLossRefresh(bar, openOrders.ToList(), position);
      else if (position != 0 && _openingPosition == 0 && openOrders.Count == 0 && CurrentOrder == null) {
        PlaceProtectiveOrders($"Emergency protection for orphaned position at barIdx {_barIndex}");
      }

      if (_barIndex < _emaSlow.Window.Size || !_rsi.IsReady || !_emaSlow.IsReady || !_bbands.IsReady || _emaFast.Window.Count < _backCandles + 1 || _emaSlow.Window.Count < _backCandles + 1) {
        LogVbt(tic, bar, barStartTime, 0);
        return;
      }

      bool upTrend = true, downTrend = true;
      try {
        for (int j = 0; j < _backCandles; j++) {
          var fastValue = _emaFast[j + 1].Value;
          var slowValue = _emaSlow[j + 1].Value;
          if (double.IsNaN((double)fastValue) || double.IsNaN((double)slowValue) || fastValue == 0 || slowValue == 0) {
            Log($"Invalid indicator values at index {j + 1}: EMAFast={fastValue}, EMASlow={slowValue}");
            LogVbt(tic, bar, barStartTime, 0);
            return;
          }
          upTrend &= fastValue > slowValue;
          downTrend &= fastValue < slowValue;
        }
      } catch (Exception ex) {
        Log($"Error in trend calculation: {ex.Message}");
        LogVbt(tic, bar, barStartTime, 0);
        return;
      }

      int totalSignal = 0;
      if (upTrend && bar.Close <= _bbands.LowerBand.Current.Value && _rsi.Current.Value < 30m) {
        totalSignal = 2; // Buy signal: EMA uptrend + close <= BBL + RSI < 32.02
        // Log($"Buy signal: upTrend={upTrend}, close={bar.Close}, BBL={_bbands.LowerBand.Current.Value}, RSI={currentRsi}");
      } else if (downTrend && bar.Close >= _bbands.UpperBand.Current.Value && _rsi.Current.Value > 70m) {
        totalSignal = 1; // Sell signal: EMA downtrend + close >= BBU + RSI > 67.98
        // Log($"Sell signal: downTrend={downTrend}, close={bar.Close}, BBU={_bbands.UpperBand.Current.Value}, RSI={currentRsi}");
      }

      LogVbt(tic, bar, barStartTime, totalSignal);

      if (totalSignal == 0 || Portfolio.Invested || Transactions.GetOpenOrders(_qcSymbol).Count > 0 || _openingPosition != 0)
        return;

      bool isLong = totalSignal == 2;
      if (!isLong) return; // Only process long signals, matching Python version behavior

      _openingPosition = totalSignal;
      var freeCashPct = isLong ? 1.0m : -1.0m;
      _entryLaddering = new EntryLadderInfo[(int)_tradingParams["n_entries"]];

      for (int i = 0; i < _tradingParams["n_entries"]; i++) {
        _entryLaddering[i] = new EntryLadderInfo {
          BarIndex = _barIndex + (int)_tradingParams["num_step_entries"] * i,
          AssetValuePct = freeCashPct / (_tradingParams["n_entries"] - i)
        };
      }

      ProcessEntry(bar);
    }

    private void ProcessEntry(TradeBar bar) {
      var entryIdx = Array.FindIndex(_entryLaddering, e => e.BarIndex == _barIndex);
      if (entryIdx == -1 || CurrentOrder != null) return; // Only process if no active order

      if (entryIdx == 0 && Portfolio.Invested)
        throw new Exception("Portfolio already invested at first entry");

      var assetValuePct = _entryLaddering[entryIdx].AssetValuePct;
      var adjustment = _symbolProperties.MinimumPriceVariation * 2;
      var valPrice = assetValuePct > 0 ? _bid!.Value - adjustment : _ask!.Value + adjustment;
      _entryLaddering[entryIdx].Price = valPrice;

      var n = _barIndex - _entryLaddering[0].BarIndex + 1;
      var avgAtr = _atr.Samples > 0 ? _atr.Take((int)Math.Min(n, _atr.Samples)).Select(idp => idp.Value).Average() : 0;
      var avgClose = _rollingConsolidatedBar.Take(Math.Min(n, _rollingConsolidatedBar.Count)).Average(b => b.Close);
      var initPosition = Portfolio[_qcSymbol].Quantity;

      var cashBalance = Portfolio.CashBook[_symbolProperties.QuoteCurrency].Amount;
      var orderCash = Math.Abs(_mySize * cashBalance * _myLeverage * assetValuePct);
      var orderSize = (orderCash / (valPrice * (1 + _myFees)));
      var orderSizePrecision = _symbolProperties.LotSize < 1 ? (int)-Math.Log10((double)_symbolProperties.LotSize) : 0;
      decimal multiplier = (decimal)Math.Pow(10, orderSizePrecision);
      orderSize = Math.Truncate(orderSize * multiplier) / multiplier;
      if (orderSize <= 0) {
        _openingPosition = 0;
        return;
      }

      _entryLaddering[entryIdx].Cost = valPrice * orderSize;

      var accumulatedCost = _entryLaddering.Take(entryIdx + 1).Sum(e => e.Cost);
      var entryPrice = Math.Abs(accumulatedCost / (orderSize != 0 ? orderSize : 1));

      var maxPriceDiff = Math.Abs(_mySize * _myLeverage * cashBalance / orderSize);
      if (_openingPosition == 2) {
        _longStopLoss = Math.Max(entryPrice - maxPriceDiff, avgClose - _tradingParams["atr_sl_ratio"] * avgAtr);
        _longTakeProfit = avgClose + _tradingParams["atr_tp_ratio"] * avgAtr;
        if (_longStopLoss < 0 || entryPrice <= _longStopLoss || Math.Abs(entryPrice - _longStopLoss) / _tradingParams["sl_levels"] <= _symbolProperties.MinimumPriceVariation ||
            Math.Abs(entryPrice - _longTakeProfit) / _tradingParams["tp_levels"] <= _symbolProperties.MinimumPriceVariation) {
          _openingPosition = 0;
          return;
        }
      } else {
        _shortStopLoss = Math.Min(entryPrice + maxPriceDiff, avgClose + _tradingParams["atr_sl_ratio"] * avgAtr);
        _shortTakeProfit = avgClose - _tradingParams["atr_tp_ratio"] * avgAtr;
        if (_shortTakeProfit < 0 || _shortStopLoss <= entryPrice || Math.Abs(entryPrice - _shortStopLoss) / _tradingParams["sl_levels"] <= _symbolProperties.MinimumPriceVariation ||
            Math.Abs(entryPrice - _shortTakeProfit) / _tradingParams["tp_levels"] <= _symbolProperties.MinimumPriceVariation) {
          _openingPosition = 0;
          return;
        }
      }

      var stopLoss = _openingPosition == 2 ? _longStopLoss : _shortStopLoss;
      var takeProfit = _openingPosition == 2 ? _longTakeProfit : _shortTakeProfit;
      var slValid = PrecheckLaddering(entryPrice, stopLoss, (int)_tradingParams["sl_levels"]);
      var tpValid = PrecheckLaddering(entryPrice, takeProfit, (int)_tradingParams["tp_levels"]);
      if (!slValid || !tpValid) {
        Log($"Laddering setup aborted: SL_valid={slValid}, TP_valid={tpValid}, SL={stopLoss.ToString(_priceDecimalFormat)}, TP={takeProfit.ToString(_priceDecimalFormat)}, entryPrice={entryPrice.ToString(_priceDecimalFormat)}");
        _openingPosition = 0;
        return;
      }

      var orderProperties = new BinanceOrderProperties { PostOnly = true };
      var quantity = orderSize * (assetValuePct < 0 ? -1 : 1);
      quantity = Math.Truncate(quantity * multiplier) / multiplier;

      CurrentOrder = null; // Ensure no active order is set, allowing queue processing
      Log($"barIdx={_barIndex}, entryIdx={entryIdx}, Enqueued OpenPosition order, QTY={quantity}, price={valPrice}");

      _pendingOrders.Enqueue(("OpenPosition", 0, entryIdx, quantity, valPrice, false, $"Entry order {entryIdx + 1} of {_tradingParams["n_entries"]}")); // Quantity and price calculated later
    }

    private string GetUtcTime() {
      return DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff");
    }

    private struct EntryLadderInfo {
      public int BarIndex;
      public decimal Price;
      public decimal AssetValuePct;
      public decimal Cost;
      public decimal PositionChanged;
    }

    private struct ExitLadderInfo {
      public decimal Price;
      public decimal Amount;
    }

    private void EnqueueCancelOrdersWithProperty(string property, string reason) {
      var openOrders = Transactions.GetOpenOrders();
      foreach (var order in openOrders) {
        if (!string.IsNullOrEmpty(order.Tag) && order.Tag.Contains(property)) {
          var orderTagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(order.Tag)
                            ?? new Dictionary<string, object>();
          orderTagDict["Reason"] = reason;
          _pendingCancelRequests.Enqueue((order.Id, orderTagDict, reason));
        }
      }
      if (openOrders.Count > 0)
        Log($"Enqueued cancellation for {openOrders.Count} orders with property {property}: {reason}");
    }

    public void EnqueueTestOrder(string property, int ladderIndex, int entryIndex, decimal quantity, decimal price, bool isStopLimit, string reason) {
      _pendingOrders.Enqueue((property, ladderIndex, entryIndex, quantity, price, isStopLimit, reason));
      Log($"Enqueued test order: {property}, QTY={quantity}, Price={price}, IsStopLimit={isStopLimit}, Reason={reason}");
    }
  }

  public class VillaFeeModel : FeeModel {
    public override OrderFee GetOrderFee(OrderFeeParameters parameters) {
      var order = parameters.Order;
      var security = parameters.Security;
      var orderValue = Math.Abs(order.Quantity * security.Price);

      if (!string.IsNullOrEmpty(order.Tag) && order.Tag.Contains("NextOrderSize"))
        return new OrderFee(new CashAmount(0, security.QuoteCurrency.Symbol));

      var feeAmount = 0m; // Maker fees set to 0
      return new OrderFee(new CashAmount(feeAmount, security.QuoteCurrency.Symbol));
    }
  }

  public class CustomCryptoData : Tick {
    public CustomCryptoData() {
    }

    public CustomCryptoData(Symbol symbol, DateTime time, decimal value, decimal quantity, TickType tickType = TickType.Trade, decimal bidPrice = 0, decimal askPrice = 0) {
      Symbol = symbol;
      Time = time;
      Value = value;
      Quantity = quantity;
      TickType = tickType;
      if (tickType == TickType.Quote) {
        BidPrice = bidPrice;
        AskPrice = askPrice;
      }
      Exchange = "Binance";
    }

    public override SubscriptionDataSource GetSource(SubscriptionDataConfig config, DateTime date, bool isLiveMode) {
      var tickTypeString = config.TickType.ToString().ToLower();
      var formattedDate = date.ToString("yyyyMMdd");
      var source = Path.Combine(Globals.DataFolder, "crypto", "binance", "tick", config.Symbol.Value.ToLower(), $"{formattedDate}_{tickTypeString}.zip");
      return new SubscriptionDataSource(source, SubscriptionTransportMedium.LocalFile, FileFormat.Csv);
    }

    public override BaseData? Reader(SubscriptionDataConfig config, string line, DateTime date, bool isLiveMode) {
      if (string.IsNullOrWhiteSpace(line) || !char.IsDigit(line[0])) {
        return null;
      }

      var data = line.Split(',');
      try {
        var price = Convert.ToDecimal(data[1]);
        var quantity = Convert.ToDecimal(data[2]);
        var timestampMicroseconds = long.Parse(data[4]);
        long ticks = timestampMicroseconds * 10; // Convert microseconds to ticks (1 microsecond = 10 ticks)
        DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        DateTime dataDateTime = epoch.AddTicks(ticks);

        TickType tickType = data.Length > 5 && data[5].Contains("quote") ? TickType.Quote : TickType.Trade;
        decimal bidPrice = tickType == TickType.Quote ? price : 0;
        decimal askPrice = tickType == TickType.Quote ? Convert.ToDecimal(data[3]) : 0; // Assuming ask price is in data[3] for quotes

        var tick = new CustomCryptoData(config.Symbol, dataDateTime, price, quantity, tickType, bidPrice, askPrice);
        return tick;
      } catch (Exception) {
        return null;
      }
    }
  }
}
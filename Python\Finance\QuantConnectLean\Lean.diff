diff --git a/Common/Brokerages/BinanceBrokerageModel.cs b/Common/Brokerages/BinanceBrokerageModel.cs
index bb4f826c8..d53b6e719 100644
--- a/Common/Brokerages/BinanceBrokerageModel.cs
+++ b/Common/Brokerages/BinanceBrokerageModel.cs
@@ -183,7 +183,7 @@ namespace QuantConnect.Brokerages
                 return false;
             }
 
-            if (security.Type != SecurityType.Crypto && security.Type != SecurityType.CryptoFuture)
+            if (security.Type != SecurityType.Base && security.Type != SecurityType.Crypto && security.Type != SecurityType.CryptoFuture)
             {
                 message = new BrokerageMessageEvent(BrokerageMessageType.Warning, "NotSupported",
                     Messages.DefaultBrokerageModel.UnsupportedSecurityType(this, security));
diff --git a/Common/Isolator.cs b/Common/Isolator.cs
index 03898b9b9..4c9577d3d 100644
--- a/Common/Isolator.cs
+++ b/Common/Isolator.cs
@@ -128,13 +128,13 @@ namespace QuantConnect
                         Log.Error(Messages.Isolator.MemoryUsageOver80Percent(sample));
                     }
 
-                    Log.Trace("Isolator.ExecuteWithTimeLimit(): " +
-                        Messages.Isolator.MemoryUsageInfo(
-                            PrettyFormatRam(memoryUsed),
-                            PrettyFormatRam((long)sample),
-                            PrettyFormatRam(OS.ApplicationMemoryUsed * 1024 * 1024),
-                            isolatorLimitResult.CurrentTimeStepElapsed,
-                            (int)Math.Ceiling(OS.CpuUsage)));
+                    //Log.Trace("Isolator.ExecuteWithTimeLimit(): " +
+                    //    Messages.Isolator.MemoryUsageInfo(
+                    //        PrettyFormatRam(memoryUsed),
+                    //        PrettyFormatRam((long)sample),
+                    //        PrettyFormatRam(OS.ApplicationMemoryUsed * 1024 * 1024),
+                    //        isolatorLimitResult.CurrentTimeStepElapsed,
+                    //        (int)Math.Ceiling(OS.CpuUsage)));
 
                     memoryLogger = utcNow.AddMinutes(1);
                 }
diff --git a/Common/Securities/CryptoFuture/CryptoFuture.cs b/Common/Securities/CryptoFuture/CryptoFuture.cs
index df808669c..63c9c8b7f 100644
--- a/Common/Securities/CryptoFuture/CryptoFuture.cs
+++ b/Common/Securities/CryptoFuture/CryptoFuture.cs
@@ -93,7 +93,7 @@ namespace QuantConnect.Securities.CryptoFuture
         /// <returns>True if the security is a crypto coin future</returns>
         private static bool IsCryptoCoinFuture(string quoteCurrency)
         {
-            return quoteCurrency != "USDT" && quoteCurrency != "BUSD";
+            return quoteCurrency != "USDT" && quoteCurrency != "BUSD" && quoteCurrency != "USDC";
         }
     }
 }
diff --git a/Engine/RealTime/LiveTradingRealTimeHandler.cs b/Engine/RealTime/LiveTradingRealTimeHandler.cs
index b5af0a85d..57c676f2e 100644
--- a/Engine/RealTime/LiveTradingRealTimeHandler.cs
+++ b/Engine/RealTime/LiveTradingRealTimeHandler.cs
@@ -99,9 +99,9 @@ namespace QuantConnect.Lean.Engine.RealTime
             {
                 var time = TimeProvider.GetUtcNow();
 
-                // pause until the next second
-                var nextSecond = time.RoundUp(TimeSpan.FromSeconds(1));
-                var delay = Convert.ToInt32((nextSecond - time).TotalMilliseconds);
+                // pause until the next millisecond
+                var nextMillisecond = time.RoundUp(TimeSpan.FromMilliseconds(1));
+                var delay = Convert.ToInt32((nextMillisecond - time).TotalMilliseconds);
                 Thread.Sleep(delay < 0 ? 1 : delay);
 
                 // poke each event to see if it should fire, we order by unique id to be deterministic
diff --git a/Engine/Results/BaseResultsHandler.cs b/Engine/Results/BaseResultsHandler.cs
index 6d0f1cb4b..5eed37826 100644
--- a/Engine/Results/BaseResultsHandler.cs
+++ b/Engine/Results/BaseResultsHandler.cs
@@ -518,7 +518,7 @@ namespace QuantConnect.Lean.Engine.Results
         /// <param name="result">The results to save</param>
         public virtual void SaveResults(string name, Result result)
         {
-            File.WriteAllText(GetResultsPath(name), JsonConvert.SerializeObject(result, Formatting.Indented, SerializerSettings));
+            // File.WriteAllText(GetResultsPath(name), JsonConvert.SerializeObject(result, Formatting.Indented, SerializerSettings));
         }
 
         /// <summary>
diff --git a/Engine/Results/LiveTradingResultHandler.cs b/Engine/Results/LiveTradingResultHandler.cs
index 7def310bd..8689b8d12 100644
--- a/Engine/Results/LiveTradingResultHandler.cs
+++ b/Engine/Results/LiveTradingResultHandler.cs
@@ -329,7 +329,7 @@ namespace QuantConnect.Lean.Engine.Results
 
                     if (_currentUtcDate != utcNow.Date)
                     {
-                        StoreOrderEvents(_currentUtcDate, GetOrderEventsToStore());
+                        // StoreOrderEvents(_currentUtcDate, GetOrderEventsToStore());
                         // start storing in a new date file
                         _currentUtcDate = utcNow.Date;
                     }
@@ -885,7 +885,7 @@ namespace QuantConnect.Lean.Engine.Results
                     if (live.Results.OrderEvents != null)
                     {
                         // we store order events separately
-                        StoreOrderEvents(_currentUtcDate, live.Results.OrderEvents);
+                        // StoreOrderEvents(_currentUtcDate, live.Results.OrderEvents);
                         // lets null the orders events so that they aren't stored again and generate a giant file
                         live.Results.OrderEvents = null;
                     }
@@ -968,8 +968,8 @@ namespace QuantConnect.Lean.Engine.Results
             Log.Trace("LiveTradingResultHandler.OrderEvent(): " + newEvent + " BrokerId: " + brokerIds, true);
             Messages.Enqueue(new OrderEventPacket(AlgorithmId, newEvent));
 
-            var message = "New Order Event: " + newEvent;
-            DebugMessage(message);
+            // var message = "New Order Event: " + newEvent;
+            // DebugMessage(message);
         }
 
         /// <summary>
diff --git a/Launcher/Properties/launchSettings.json b/Launcher/Properties/launchSettings.json
new file mode 100644
index 000000000..ee14c3d92
--- /dev/null
+++ b/Launcher/Properties/launchSettings.json
@@ -0,0 +1,8 @@
+{
+  "profiles": {
+    "QuantConnect.Lean.Launcher": {
+      "commandName": "Project",
+      "commandLineArgs": "--config D:\\work\\xstarwalker168\\Python\\Finance\\QuantConnectLean\\trading-bot-config.json --parameters tradeMode:1,symbol:ETH,currency:USDC,leverage:10 --results-destination-folder \"D:/work/xstarwalker168/Python/Finance/QuantConnectLean/QC-Log-Dir\" --algorithm-language CSharp --environment live-futures-binance --algorithm-location D:\\work\\Lean\\Launcher\\bin\\$(Configuration)\\cCryptoBot.dll --data-folder \"D:/work/xstarwalker168/Python/Finance/QuantConnectLean/Data\""
+    }
+  }
+}
\ No newline at end of file
diff --git a/QuantConnect.Lean.sln b/QuantConnect.Lean.sln
index ed4f58898..84974f042 100644
--- a/QuantConnect.Lean.sln
+++ b/QuantConnect.Lean.sln
@@ -56,6 +56,10 @@ Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QuantConnect.Optimizer.Laun
 EndProject
 Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QuantConnect.DownloaderDataProvider.Launcher", "DownloaderDataProvider\QuantConnect.DownloaderDataProvider.Launcher.csproj", "{D139191E-50D5-4284-AC9C-247ED60950F4}"
 EndProject
+Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "cCryptoBot", "..\xstarwalker168\Python\Finance\QuantConnectLean\CryptoBot(C#)\cCryptoBot\cCryptoBot.csproj", "{F2E5538B-6990-A97F-F407-3AF537EDB546}"
+EndProject
+Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QuantConnect.BinanceBrokerage", "..\Lean.Brokerages.Binance\QuantConnect.BinanceBrokerage\QuantConnect.BinanceBrokerage.csproj", "{C2EB8E39-53E7-1101-632F-118AE0F243F4}"
+EndProject
 Global
 	GlobalSection(SolutionConfigurationPlatforms) = preSolution
 		Debug|Any CPU = Debug|Any CPU
@@ -152,6 +156,14 @@ Global
 		{D139191E-50D5-4284-AC9C-247ED60950F4}.Debug|Any CPU.Build.0 = Debug|Any CPU
 		{D139191E-50D5-4284-AC9C-247ED60950F4}.Release|Any CPU.ActiveCfg = Release|Any CPU
 		{D139191E-50D5-4284-AC9C-247ED60950F4}.Release|Any CPU.Build.0 = Release|Any CPU
+		{F2E5538B-6990-A97F-F407-3AF537EDB546}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
+		{F2E5538B-6990-A97F-F407-3AF537EDB546}.Debug|Any CPU.Build.0 = Debug|Any CPU
+		{F2E5538B-6990-A97F-F407-3AF537EDB546}.Release|Any CPU.ActiveCfg = Release|Any CPU
+		{F2E5538B-6990-A97F-F407-3AF537EDB546}.Release|Any CPU.Build.0 = Release|Any CPU
+		{C2EB8E39-53E7-1101-632F-118AE0F243F4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
+		{C2EB8E39-53E7-1101-632F-118AE0F243F4}.Debug|Any CPU.Build.0 = Debug|Any CPU
+		{C2EB8E39-53E7-1101-632F-118AE0F243F4}.Release|Any CPU.ActiveCfg = Release|Any CPU
+		{C2EB8E39-53E7-1101-632F-118AE0F243F4}.Release|Any CPU.Build.0 = Release|Any CPU
 	EndGlobalSection
 	GlobalSection(SolutionProperties) = preSolution
 		HideSolutionNode = FALSE

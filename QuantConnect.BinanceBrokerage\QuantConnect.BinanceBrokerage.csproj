﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Release</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <TargetFramework>net9.0</TargetFramework>
    <Product>QuantConnect.Brokerages.Binance</Product>
    <AssemblyName>QuantConnect.Brokerages.Binance</AssemblyName>
    <RootNamespace>QuantConnect.Brokerages.Binance</RootNamespace>
    <AssemblyTitle>QuantConnect.Brokerages.Binance</AssemblyTitle>
    <OutputType>Library</OutputType>
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <Description>QuantConnect LEAN Binance Brokerage: Brokerage Binance plugin for Lean</Description>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugType>full</DebugType>
    <OutputPath>bin\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <OutputPath>bin\Release\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\..\Lean\Common\Properties\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="QuantConnect.Brokerages" Version="2.5.*" />
  </ItemGroup>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy D:\work\Lean.Brokerages.Binance\QuantConnect.BinanceBrokerage\bin\Debug\QuantConnect.Brokerages.Binance.dll D:\work\Lean\Launcher\bin\Debug /E /Y /I /F&#xD;&#xA;xcopy D:\work\Lean.Brokerages.Binance\QuantConnect.BinanceBrokerage\bin\Release\QuantConnect.Brokerages.Binance.dll D:\work\Lean\Launcher\bin\Release /E /Y /I /F&#xD;&#xA;&#xD;&#xA;del /q &quot;D:\work\xstarwalker168\Python\Finance\QuantConnectLean\QC-Log-Dir\*&quot;&#xD;&#xA;" />
  </Target>
</Project>
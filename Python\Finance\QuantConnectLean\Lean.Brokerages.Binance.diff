diff --git a/QuantConnect.BinanceBrokerage/BinanceBaseRestApiClient.cs b/QuantConnect.BinanceBrokerage/BinanceBaseRestApiClient.cs
index 44e2d4f..3baa165 100644
--- a/QuantConnect.BinanceBrokerage/BinanceBaseRestApiClient.cs
+++ b/QuantConnect.BinanceBrokerage/BinanceBaseRestApiClient.cs
@@ -24,6 +24,7 @@ using QuantConnect.Util;
 using RestSharp;
 using System;
 using System.Collections.Generic;
+using System.Diagnostics;
 using System.Globalization;
 using System.Linq;
 using System.Net;
@@ -168,7 +169,7 @@ namespace QuantConnect.Brokerages.Binance
         {
             var request = new RestRequest($"{apiPrefix}/account", Method.GET);
 
-            var response = ExecuteRestRequestWithSignature(request);
+            var response = ExecuteRestRequestWithSignature(request, null, "GetCashBalance");
             if (response.StatusCode != HttpStatusCode.OK)
             {
                 throw new Exception($"BinanceBrokerage.GetCashBalance: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
@@ -196,7 +197,7 @@ namespace QuantConnect.Brokerages.Binance
         {
             var request = new RestRequest($"{ApiPrefix}/openOrders", Method.GET);
 
-            var response = ExecuteRestRequestWithSignature(request);
+            var response = ExecuteRestRequestWithSignature(request, null, "GetOpenOrders");
             if (response.StatusCode != HttpStatusCode.OK)
             {
                 throw new Exception($"BinanceBrokerage.GetCashBalance: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
@@ -210,13 +211,13 @@ namespace QuantConnect.Brokerages.Binance
         /// </summary>
         /// <param name="order">The order to be placed</param>
         /// <returns>True if the request for a new order has been placed, false otherwise</returns>
-        public bool PlaceOrder(Order order)
+        public virtual bool PlaceOrder(Order order)
         {
             var body = CreateOrderBody(order);
 
             var request = new RestRequest($"{ApiPrefix}/order", Method.POST);
 
-            var response = ExecuteRestRequestWithSignature(request, body);
+            var response = ExecuteRestRequestWithSignature(request, body, $"{order}");
             if (response.StatusCode == HttpStatusCode.OK)
             {
                 var raw = JsonConvert.DeserializeObject<Messages.NewOrder>(response.Content);
@@ -269,13 +270,28 @@ namespace QuantConnect.Brokerages.Binance
             switch (order)
             {
                 case LimitOrder limitOrder:
-                    body["type"] = (order.Properties as BinanceOrderProperties)?.PostOnly == true
-                        ? "LIMIT_MAKER"
-                        : "LIMIT";
+                    if (order.SecurityType == SecurityType.CryptoFuture)
+                    {
+                        body["type"] = "LIMIT";
+                        if ((order.Properties as BinanceOrderProperties)?.PostOnly == true)
+                        {
+                            body["timeInForce"] = "GTX";
+                        }
+                        else
+                        {
+                            body["timeInForce"] = "GTC";
+                        }
+                    }
+                    else
+                    {
+                        body["type"] = (order.Properties as BinanceOrderProperties)?.PostOnly == true
+                            ? "LIMIT_MAKER"
+                            : "LIMIT";
+                        // timeInForce is not required for LIMIT_MAKER
+                        if (Equals(body["type"], "LIMIT"))
+                            body["timeInForce"] = "GTC";
+                    }
                     body["price"] = limitOrder.LimitPrice.ToString(CultureInfo.InvariantCulture);
-                    // timeInForce is not required for LIMIT_MAKER
-                    if (Equals(body["type"], "LIMIT"))
-                        body["timeInForce"] = "GTC";
                     break;
                 case MarketOrder:
                     body["type"] = "MARKET";
@@ -296,14 +312,14 @@ namespace QuantConnect.Brokerages.Binance
                     }
                     else if (order.SecurityType == SecurityType.CryptoFuture)
                     {
-                        body["type"] = stopPrice <= tickerPrice ? "TAKE_PROFIT" : "STOP";
+                          body["type"] = stopPrice <= tickerPrice ? "TAKE_PROFIT" : "STOP";
                     }
                     else
                     {
                         body["type"] = stopPrice <= tickerPrice ? "TAKE_PROFIT_LIMIT" : "STOP_LOSS_LIMIT";
                     }
 
-                    body["timeInForce"] = "GTC";
+                    body["timeInForce"] = order.SecurityType == SecurityType.CryptoFuture ? "GTX" : "GTC";
                     body["stopPrice"] = stopPrice.ToStringInvariant();
                     body["price"] = stopLimitOrder.LimitPrice.ToStringInvariant();
                     break;
@@ -328,7 +344,7 @@ namespace QuantConnect.Brokerages.Binance
         /// </summary>
         /// <param name="order">The order to cancel</param>
         /// <returns>True if the request was submitted for cancellation, false otherwise</returns>
-        public bool CancelOrder(Order order)
+        public virtual bool CancelOrder(Order order)
         {
             var success = new List<bool>();
             IDictionary<string, object> body = new Dictionary<string, object>()
@@ -340,7 +356,7 @@ namespace QuantConnect.Brokerages.Binance
                 body["orderId"] = id;
                 var request = new RestRequest($"{ApiPrefix}/order", Method.DELETE);
 
-                var response = ExecuteRestRequestWithSignature(request, body);
+                var response = ExecuteRestRequestWithSignature(request, body, $"CancelOrderId: {order.Id}, BrokerId: {id}");
                 success.Add(response.StatusCode == HttpStatusCode.OK);
             }
 
@@ -383,7 +399,7 @@ namespace QuantConnect.Brokerages.Binance
                 var timeframe = $"&startTime={startMs}&endTime={endMs}";
 
                 var restRequest = new RestRequest(endpoint + timeframe, Method.GET);
-                var response = ExecuteRestRequest(restRequest);
+                var response = ExecuteRestRequest(restRequest, $"GetHistory: {symbol}");
 
                 if (response.StatusCode != HttpStatusCode.OK)
                 {
@@ -426,7 +442,7 @@ namespace QuantConnect.Brokerages.Binance
             var endpoint = TickerPriceChangeStatisticsEndpoint;
             var request = new RestRequest(endpoint, Method.GET);
 
-            var response = ExecuteRestRequest(request);
+            var response = ExecuteRestRequest(request, "GetTickerPriceChangeStatistics");
             if (response.StatusCode != HttpStatusCode.OK)
             {
                 throw new Exception($"BinanceBrokerage.GetCashBalance: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
@@ -454,7 +470,7 @@ namespace QuantConnect.Brokerages.Binance
                 ParameterType.RequestBody
             );
 
-            var pong = ExecuteRestRequest(ping);
+            var pong = ExecuteRestRequest(ping, "SessionKeepAlive");
             return pong.StatusCode == HttpStatusCode.OK;
         }
 
@@ -472,7 +488,7 @@ namespace QuantConnect.Brokerages.Binance
                     Encoding.UTF8.GetBytes($"listenKey={SessionId}"),
                     ParameterType.RequestBody
                 );
-                if (ExecuteRestRequest(request).StatusCode == HttpStatusCode.OK)
+                if (ExecuteRestRequest(request, "StopSession").StatusCode == HttpStatusCode.OK)
                 {
                     SessionId = null;
                 }
@@ -487,7 +503,7 @@ namespace QuantConnect.Brokerages.Binance
         {
             var endpoint = $"{GetBaseDataEndpoint()}/ticker/price";
             var req = new RestRequest(endpoint, Method.GET);
-            var response = ExecuteRestRequest(req);
+            var response = ExecuteRestRequest(req, "GetTickers");
             if (response.StatusCode != HttpStatusCode.OK)
             {
                 throw new Exception($"BinanceBrokerage.GetTick: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
@@ -504,7 +520,7 @@ namespace QuantConnect.Brokerages.Binance
             var request = new RestRequest(UserDataStreamEndpoint, Method.POST);
             request.AddHeader(KeyHeader, ApiKey);
 
-            var response = ExecuteRestRequest(request);
+            var response = ExecuteRestRequest(request, "CreateListenKey");
             if (response.StatusCode != HttpStatusCode.OK)
             {
                 throw new Exception($"BinanceBrokerage.StartSession: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
@@ -535,20 +551,22 @@ namespace QuantConnect.Brokerages.Binance
         /// </summary>
         /// <param name="request">The REST request to execute.</param>
         /// <param name="body">Optional request body parameters.</param>
+        /// <param name="context">Optional context information for logging.</param>
         /// <returns>The response from the REST API.</returns>
-        protected IRestResponse ExecuteRestRequestWithSignature(IRestRequest request, IDictionary<string, object> body = default)
+        protected IRestResponse ExecuteRestRequestWithSignature(IRestRequest request, IDictionary<string, object> body = default, string context = null)
         {
-            return ExecuteRestRequestInternal(request, body, true);
+            return ExecuteRestRequestInternal(request, body, true, context);
         }
 
         /// <summary>
         /// Executes a REST request while handling rate limits and retries on HTTP 429 responses.
         /// </summary>
         /// <param name="request">The REST request to execute.</param>
+        /// <param name="context">Optional context information for logging.</param>
         /// <returns>The response from the REST API.</returns>
-        protected IRestResponse ExecuteRestRequest(IRestRequest request)
+        protected IRestResponse ExecuteRestRequest(IRestRequest request, string context = null)
         {
-            return ExecuteRestRequestInternal(request, null, false);
+            return ExecuteRestRequestInternal(request, null, false, context);
         }
 
         /// <summary>
@@ -558,12 +576,14 @@ namespace QuantConnect.Brokerages.Binance
         /// <param name="request">The REST request to execute.</param>
         /// <param name="body">Optional request body parameters.</param>
         /// <param name="useSignature">Indicates whether to include a signature in the request.</param>
+        /// <param name="context">Optional context information for logging.</param>
         /// <returns>The response from the REST API.</returns>
-        private IRestResponse ExecuteRestRequestInternal(IRestRequest request, IDictionary<string, object> body, bool useSignature)
+        private IRestResponse ExecuteRestRequestInternal(IRestRequest request, IDictionary<string, object> body, bool useSignature, string context = null)
         {
             const int maxAttempts = 10;
             var attempts = 0;
             IRestResponse response;
+            var totalStopwatch = Stopwatch.StartNew();
 
             do
             {
@@ -599,15 +619,27 @@ namespace QuantConnect.Brokerages.Binance
                     }
                 }
 
+                var requestStopwatch = Stopwatch.StartNew();
                 response = _restClient.Execute(request);
+                requestStopwatch.Stop();
+
+                var contextInfo = !string.IsNullOrEmpty(context) ? $" [{context}]" : "";
+                Log.Trace($"BinanceRestApi{contextInfo}: {request.Method} {request.Resource} completed in {requestStopwatch.ElapsedMilliseconds}ms (Status: {response.StatusCode}, Attempt: {attempts + 1})");
 
                 // Retry on HTTP 429 (Too Many Requests), with exponential backoff using WaitOne as an additional safeguard.
             } while (++attempts < maxAttempts && (int)response.StatusCode == 429 && !_cancellationToken.Token.WaitHandle.WaitOne(TimeSpan.FromSeconds(1 * attempts)));
 
+            totalStopwatch.Stop();
+            if (attempts > 1)
+            {
+                var contextInfo = !string.IsNullOrEmpty(context) ? $" [{context}]" : "";
+                Log.Trace($"BinanceRestApi{contextInfo}: {request.Method} {request.Resource} total time with retries: {totalStopwatch.ElapsedMilliseconds}ms (Attempts: {attempts})");
+            }
+
             return response;
         }
 
-        private decimal GetTickerPrice(Order order)
+        protected decimal GetTickerPrice(Order order)
         {
             var security = _securityProvider.GetSecurity(order.Symbol);
             var tickerPrice = order.Direction == OrderDirection.Buy ? security.AskPrice : security.BidPrice;
@@ -682,7 +714,7 @@ namespace QuantConnect.Brokerages.Binance
         /// </summary>
         /// <param name="newOrder">The brokerage order submit result</param>
         /// <param name="order">The lean order</param>
-        private void OnOrderSubmit(Messages.NewOrder newOrder, Order order)
+        protected void OnOrderSubmit(Messages.NewOrder newOrder, Order order)
         {
             try
             {
@@ -710,7 +742,7 @@ namespace QuantConnect.Brokerages.Binance
         /// Event invocator for the OrderFilled event
         /// </summary>
         /// <param name="e">The OrderEvent</param>
-        private void OnOrderEvent(OrderEvent e)
+        protected void OnOrderEvent(OrderEvent e)
         {
             try
             {
diff --git a/QuantConnect.BinanceBrokerage/BinanceBrokerage.Messaging.cs b/QuantConnect.BinanceBrokerage/BinanceBrokerage.Messaging.cs
index f21be71..9453160 100644
--- a/QuantConnect.BinanceBrokerage/BinanceBrokerage.Messaging.cs
+++ b/QuantConnect.BinanceBrokerage/BinanceBrokerage.Messaging.cs
@@ -1,231 +1,237 @@
-/*
- * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
- * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
- *
- * Licensed under the Apache License, Version 2.0 (the "License");
- * you may not use this file except in compliance with the License.
- * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
- *
- * Unless required by applicable law or agreed to in writing, software
- * distributed under the License is distributed on an "AS IS" BASIS,
- * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- * See the License for the specific language governing permissions and
- * limitations under the License.
-*/
-
-using QuantConnect.Brokerages.Binance.Messages;
-using QuantConnect.Data.Market;
-using QuantConnect.Logging;
-using QuantConnect.Orders;
-using QuantConnect.Orders.Fees;
-using QuantConnect.Securities;
-using System;
-using Newtonsoft.Json.Linq;
-using QuantConnect.Data;
-using System.Linq;
-
-namespace QuantConnect.Brokerages.Binance
-{
-    public partial class BinanceBrokerage
-    {
-        private IDataAggregator _aggregator;
-
-        /// <summary>
-        /// Locking object for the Ticks list in the data queue handler
-        /// </summary>
-        protected readonly object TickLocker = new object();
-
-        private void OnUserMessage(WebSocketMessage webSocketMessage)
-        {
-            var e = (WebSocketClientWrapper.TextMessage)webSocketMessage.Data;
-
-            try
-            {
+/*
+ * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
+ * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
+ *
+ * Licensed under the Apache License, Version 2.0 (the "License");
+ * you may not use this file except in compliance with the License.
+ * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
+ *
+ * Unless required by applicable law or agreed to in writing, software
+ * distributed under the License is distributed on an "AS IS" BASIS,
+ * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
+ * See the License for the specific language governing permissions and
+ * limitations under the License.
+*/
+
+using QuantConnect.Brokerages.Binance.Messages;
+using QuantConnect.Data.Market;
+using QuantConnect.Logging;
+using QuantConnect.Orders;
+using QuantConnect.Orders.Fees;
+using QuantConnect.Securities;
+using System;
+using Newtonsoft.Json.Linq;
+using QuantConnect.Data;
+using System.Linq;
+
+namespace QuantConnect.Brokerages.Binance
+{
+    public partial class BinanceBrokerage
+    {
+        private IDataAggregator _aggregator;
+
+        /// <summary>
+        /// Locking object for the Ticks list in the data queue handler
+        /// </summary>
+        protected readonly object TickLocker = new object();
+
+        private void OnUserMessage(WebSocketMessage webSocketMessage)
+        {
+            var e = (WebSocketClientWrapper.TextMessage)webSocketMessage.Data;
+
+            try
+            {
                 if (Log.DebuggingEnabled)
-                {
+                {
                     Log.Debug($"BinanceBrokerage.OnUserMessage(): {e.Message}");
-                }
-
-                var obj = JObject.Parse(e.Message);
-
-                var objError = obj["error"];
-                if (objError != null)
-                {
-                    var error = objError.ToObject<ErrorMessage>();
-                    OnMessage(new BrokerageMessageEvent(BrokerageMessageType.Error, error.Code, error.Message));
-                    return;
-                }
-
-                var objData = obj;
-
-                var objEventType = objData["e"];
-                if (objEventType != null)
-                {
-                    var eventType = objEventType.ToObject<string>();
-
-                    Execution execution = null;
-                    switch (eventType)
-                    {
-                        case "executionReport":
-                            execution = objData.ToObject<Execution>();
-                            break;
-                        case "ORDER_TRADE_UPDATE":
-                            execution = objData["o"].ToObject<Execution>();
-                            break;
-                    }
-
-                    if (execution != null && (execution.ExecutionType.Equals("TRADE", StringComparison.OrdinalIgnoreCase)
-                        || execution.ExecutionType.Equals("EXPIRED", StringComparison.OrdinalIgnoreCase)))
-                    {
-                        OnFillOrder(execution);
-                    }
-                }
-            }
-            catch (Exception exception)
-            {
-                OnMessage(new BrokerageMessageEvent(BrokerageMessageType.Error, -1, $"Parsing wss message failed. Data: {e.Message} Exception: {exception}"));
-                throw;
-            }
-        }
-
-        private void OnDataMessage(WebSocketMessage webSocketMessage)
-        {
-            var e = (WebSocketClientWrapper.TextMessage)webSocketMessage.Data;
-
-            try
-            {
-                var obj = JObject.Parse(e.Message);
-
-                var objError = obj["error"];
-                if (objError != null)
-                {
-                    var error = objError.ToObject<ErrorMessage>();
-                    OnMessage(new BrokerageMessageEvent(BrokerageMessageType.Error, error.Code, error.Message));
-                    return;
-                }
-
-                var objData = obj;
-
-                var objEventType = objData["e"];
-                if (objEventType != null)
-                {
-                    var eventType = objEventType.ToObject<string>();
-
-                    switch (eventType)
-                    {
-                        case "trade":
-                        case "aggTrade":
-                            var trade = objData.ToObject<Trade>();
-                            // futures feed send upper and lower case T confusing json
-                            trade.Time = objData["T"].ToObject<long>();
-                            EmitTradeTick(
-                                _symbolMapper.GetLeanSymbol(trade.Symbol, GetSupportedSecurityType(), MarketName),
-                                Time.UnixMillisecondTimeStampToDateTime(trade.Time),
-                                trade.Price,
-                                trade.Quantity);
-                            break;
-                        case "bookTicker":
-                            // futures stream the event type but spot doesn't, that's why we have the next 'else if'
-                            HandleQuoteTick(objData);
-                            break;
-                    }
-                }
-                else if (objData["u"] != null)
-                {
-                    HandleQuoteTick(objData);
-                }
-            }
-            catch (Exception exception)
-            {
-                OnMessage(new BrokerageMessageEvent(BrokerageMessageType.Error, -1, $"Parsing wss message failed. Data: {e.Message} Exception: {exception}"));
-                throw;
-            }
-        }
-
-        private void HandleQuoteTick(JObject objData)
-        {
-            var quote = objData.ToObject<BestBidAskQuote>();
-            EmitQuoteTick(
-                _symbolMapper.GetLeanSymbol(quote.Symbol, GetSupportedSecurityType(), MarketName),
-                quote.BestBidPrice,
-                quote.BestBidSize,
-                quote.BestAskPrice,
-                quote.BestAskSize);
-        }
-
-        private void EmitQuoteTick(Symbol symbol, decimal bidPrice, decimal bidSize, decimal askPrice, decimal askSize)
-        {
-            var tick = new Tick
-            {
-                AskPrice = askPrice,
-                BidPrice = bidPrice,
-                Time = DateTime.UtcNow,
-                Symbol = symbol,
-                TickType = TickType.Quote,
-                AskSize = askSize,
-                BidSize = bidSize
-            };
-            tick.SetValue();
-
-            lock (TickLocker)
-            {
-                _aggregator.Update(tick);
-            }
-        }
-
-        private void EmitTradeTick(Symbol symbol, DateTime time, decimal price, decimal quantity)
-        {
-            var tick = new Tick
-            {
-                Symbol = symbol,
-                Value = price,
-                Quantity = Math.Abs(quantity),
-                Time = time,
-                TickType = TickType.Trade
-            };
-
-            lock (TickLocker)
-            {
-                _aggregator.Update(tick);
-            }
-        }
-
-        private void OnFillOrder(Execution data)
-        {
-            try
-            {
-                var order = _algorithm.Transactions.GetOrdersByBrokerageId(data.OrderId)?.SingleOrDefault();
-                if (order == null)
-                {
-                    // not our order, nothing else to do here
-                    Log.Error($"BinanceBrokerage.OnFillOrder(): order not found: {data.OrderId}");
-                    return;
-                }
-
-                var fillPrice = data.LastExecutedPrice;
-                var fillQuantity = data.Direction == OrderDirection.Sell ? -data.LastExecutedQuantity : data.LastExecutedQuantity;
-                var updTime = Time.UnixMillisecondTimeStampToDateTime(data.TransactionTime);
-                var orderFee = OrderFee.Zero;
-                if (!string.IsNullOrEmpty(data.FeeCurrency))
-                {
-                    // might not be sent if zero fee
-                    orderFee = new OrderFee(new CashAmount(data.Fee, data.FeeCurrency));
-                }
-                var status = ConvertOrderStatus(data.OrderStatus);
-                var orderEvent = new OrderEvent
-                (
-                    order.Id, order.Symbol, updTime, status,
-                    data.Direction, fillPrice, fillQuantity,
-                    orderFee, $"Binance Order Event {data.Direction}"
-                );
-
-                OnOrderEvent(orderEvent);
-            }
-            catch (Exception e)
-            {
-                Log.Error(e);
-                throw;
-            }
-        }
-    }
-}
+                }
+
+                var obj = JObject.Parse(e.Message);
+
+                var objError = obj["error"];
+                if (objError != null)
+                {
+                    var error = objError.ToObject<ErrorMessage>();
+                    OnMessage(new BrokerageMessageEvent(BrokerageMessageType.Error, error.Code, error.Message));
+                    return;
+                }
+
+                var objData = obj;
+
+                var objEventType = objData["e"];
+                if (objEventType != null)
+                {
+                    var eventType = objEventType.ToObject<string>();
+
+                    Execution execution = null;
+                    switch (eventType)
+                    {
+                        case "executionReport":
+                            execution = objData.ToObject<Execution>();
+                            break;
+                        case "ORDER_TRADE_UPDATE":
+                            execution = objData["o"].ToObject<Execution>();
+                            break;
+                        case "CONDITIONAL_ORDER_TRIGGER_REJECT":
+                            execution = objData["or"].ToObject<Execution>();
+                            execution.OrderStatus = "REJECTED";
+                            break;
+                    }
+
+                    if (execution != null && (eventType == "CONDITIONAL_ORDER_TRIGGER_REJECT"
+                        || execution.ExecutionType.Equals("TRADE", StringComparison.OrdinalIgnoreCase)
+                        || execution.ExecutionType.Equals("EXPIRED", StringComparison.OrdinalIgnoreCase)
+                        ))
+                    {
+                        OnFillOrder(execution);
+                    }
+                }
+            }
+            catch (Exception exception)
+            {
+                OnMessage(new BrokerageMessageEvent(BrokerageMessageType.Error, -1, $"Parsing wss message failed. Data: {e.Message} Exception: {exception}"));
+                throw;
+            }
+        }
+
+        private void OnDataMessage(WebSocketMessage webSocketMessage)
+        {
+            var e = (WebSocketClientWrapper.TextMessage)webSocketMessage.Data;
+
+            try
+            {
+                var obj = JObject.Parse(e.Message);
+
+                var objError = obj["error"];
+                if (objError != null)
+                {
+                    var error = objError.ToObject<ErrorMessage>();
+                    OnMessage(new BrokerageMessageEvent(BrokerageMessageType.Error, error.Code, error.Message));
+                    return;
+                }
+
+                var objData = obj;
+
+                var objEventType = objData["e"];
+                if (objEventType != null)
+                {
+                    var eventType = objEventType.ToObject<string>();
+
+                    switch (eventType)
+                    {
+                        case "trade":
+                        case "aggTrade":
+                            var trade = objData.ToObject<Trade>();
+                            // futures feed send upper and lower case T confusing json
+                            trade.Time = objData["T"].ToObject<long>();
+                            EmitTradeTick(
+                                _symbolMapper.GetLeanSymbol(trade.Symbol, GetSupportedSecurityType(), MarketName),
+                                Time.UnixMillisecondTimeStampToDateTime(trade.Time),
+                                trade.Price,
+                                trade.Quantity);
+                            break;
+                        case "bookTicker":
+                            // futures stream the event type but spot doesn't, that's why we have the next 'else if'
+                            HandleQuoteTick(objData);
+                            break;
+                    }
+                }
+                else if (objData["u"] != null)
+                {
+                    HandleQuoteTick(objData);
+                }
+            }
+            catch (Exception exception)
+            {
+                OnMessage(new BrokerageMessageEvent(BrokerageMessageType.Error, -1, $"Parsing wss message failed. Data: {e.Message} Exception: {exception}"));
+                throw;
+            }
+        }
+
+        private void HandleQuoteTick(JObject objData)
+        {
+            var quote = objData.ToObject<BestBidAskQuote>();
+            EmitQuoteTick(
+                _symbolMapper.GetLeanSymbol(quote.Symbol, GetSupportedSecurityType(), MarketName),
+                quote.BestBidPrice,
+                quote.BestBidSize,
+                quote.BestAskPrice,
+                quote.BestAskSize);
+        }
+
+        private void EmitQuoteTick(Symbol symbol, decimal bidPrice, decimal bidSize, decimal askPrice, decimal askSize)
+        {
+            var tick = new Tick
+            {
+                AskPrice = askPrice,
+                BidPrice = bidPrice,
+                Time = DateTime.UtcNow,
+                Symbol = symbol,
+                TickType = TickType.Quote,
+                AskSize = askSize,
+                BidSize = bidSize
+            };
+            tick.SetValue();
+
+            lock (TickLocker)
+            {
+                _aggregator.Update(tick);
+            }
+        }
+
+        private void EmitTradeTick(Symbol symbol, DateTime time, decimal price, decimal quantity)
+        {
+            var tick = new Tick
+            {
+                Symbol = symbol,
+                Value = price,
+                Quantity = Math.Abs(quantity),
+                Time = time,
+                TickType = TickType.Trade
+            };
+
+            lock (TickLocker)
+            {
+                _aggregator.Update(tick);
+            }
+        }
+
+        private void OnFillOrder(Execution data)
+        {
+            try
+            {
+                var order = _algorithm.Transactions.GetOrdersByBrokerageId(data.OrderId)?.SingleOrDefault();
+                if (order == null)
+                {
+                    // not our order, nothing else to do here
+                    Log.Error($"BinanceBrokerage.OnFillOrder(): order not found: {data.OrderId}");
+                    return;
+                }
+
+                var fillPrice = data.LastExecutedPrice;
+                var fillQuantity = order.Direction == OrderDirection.Sell ? -data.LastExecutedQuantity : data.LastExecutedQuantity;
+                var updTime = Time.UnixMillisecondTimeStampToDateTime(data.TransactionTime);
+                var orderFee = OrderFee.Zero;
+                if (!string.IsNullOrEmpty(data.FeeCurrency))
+                {
+                    // might not be sent if zero fee
+                    orderFee = new OrderFee(new CashAmount(data.Fee, data.FeeCurrency));
+                }
+                var status = ConvertOrderStatus(data.OrderStatus);
+                var orderEvent = new OrderEvent
+                (
+                    order.Id, order.Symbol, updTime, status,
+                    order.Direction, fillPrice, fillQuantity,
+                    orderFee, $"Binance Order Event {order.Direction}"
+                );
+
+                OnOrderEvent(orderEvent);
+            }
+            catch (Exception e)
+            {
+                Log.Error(e);
+                throw;
+            }
+        }
+    }
+}
diff --git a/QuantConnect.BinanceBrokerage/BinanceBrokerage.Utility.cs b/QuantConnect.BinanceBrokerage/BinanceBrokerage.Utility.cs
index a76e9fb..390bc5d 100644
--- a/QuantConnect.BinanceBrokerage/BinanceBrokerage.Utility.cs
+++ b/QuantConnect.BinanceBrokerage/BinanceBrokerage.Utility.cs
@@ -1,53 +1,53 @@
-/*
- * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
- * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
- *
- * Licensed under the Apache License, Version 2.0 (the "License");
- * you may not use this file except in compliance with the License.
- * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
- *
- * Unless required by applicable law or agreed to in writing, software
- * distributed under the License is distributed on an "AS IS" BASIS,
- * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- * See the License for the specific language governing permissions and
- * limitations under the License.
-*/
-
-using QuantConnect.Orders;
-
-namespace QuantConnect.Brokerages.Binance
-{
-    /// <summary>
-    /// Binance utility methods
-    /// </summary>
-    public partial class BinanceBrokerage
-    {
-        private static OrderStatus ConvertOrderStatus(string raw)
-        {
-            switch (raw.LazyToUpper())
-            {
-                case "NEW":
-                    return OrderStatus.Submitted;
-
-                case "PARTIALLY_FILLED":
-                    return OrderStatus.PartiallyFilled;
-
-                case "FILLED":
-                    return OrderStatus.Filled;
-
-                case "PENDING_CANCEL":
-                    return OrderStatus.CancelPending;
-
-                case "CANCELED":
-                    return OrderStatus.Canceled;
-
-                case "REJECTED":
-                case "EXPIRED":
-                    return OrderStatus.Invalid;
-
-                default:
-                    return OrderStatus.None;
-            }
-        }
-    }
-}
+/*
+ * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
+ * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
+ *
+ * Licensed under the Apache License, Version 2.0 (the "License");
+ * you may not use this file except in compliance with the License.
+ * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
+ *
+ * Unless required by applicable law or agreed to in writing, software
+ * distributed under the License is distributed on an "AS IS" BASIS,
+ * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
+ * See the License for the specific language governing permissions and
+ * limitations under the License.
+*/
+
+using QuantConnect.Orders;
+
+namespace QuantConnect.Brokerages.Binance
+{
+    /// <summary>
+    /// Binance utility methods
+    /// </summary>
+    public partial class BinanceBrokerage
+    {
+        private static OrderStatus ConvertOrderStatus(string raw)
+        {
+            switch (raw.LazyToUpper())
+            {
+                case "NEW":
+                    return OrderStatus.Submitted;
+
+                case "PARTIALLY_FILLED":
+                    return OrderStatus.PartiallyFilled;
+
+                case "FILLED":
+                    return OrderStatus.Filled;
+
+                case "PENDING_CANCEL":
+                    return OrderStatus.CancelPending;
+
+                case "CANCELED":
+                    return OrderStatus.Canceled;
+
+                case "REJECTED":
+                    return OrderStatus.Invalid;
+
+                case "EXPIRED":
+                default:
+                    return OrderStatus.None;
+            }
+        }
+    }
+}
diff --git a/QuantConnect.BinanceBrokerage/BinanceBrokerage.cs b/QuantConnect.BinanceBrokerage/BinanceBrokerage.cs
index a5153ad..c68e5ae 100644
--- a/QuantConnect.BinanceBrokerage/BinanceBrokerage.cs
+++ b/QuantConnect.BinanceBrokerage/BinanceBrokerage.cs
@@ -538,7 +538,7 @@ namespace QuantConnect.Brokerages.Binance
                 throw new InvalidOperationException("Binance.US doesn't support SPOT Testnet trading.");
             }
 
-            ValidateSubscription();
+            // ValidateSubscription();
 
             _webApiRateLimiter = GetRateLimiter(job is null ? DeploymentTarget.LocalPlatform : job.DeploymentTarget);
             base.Initialize(wssUrl, new WebSocketClientWrapper(), null, apiKey, apiSecret);
diff --git a/QuantConnect.BinanceBrokerage/BinanceCrossMarginRestApiClient.cs b/QuantConnect.BinanceBrokerage/BinanceCrossMarginRestApiClient.cs
index dcb162b..a0f5fdf 100644
--- a/QuantConnect.BinanceBrokerage/BinanceCrossMarginRestApiClient.cs
+++ b/QuantConnect.BinanceBrokerage/BinanceCrossMarginRestApiClient.cs
@@ -1,73 +1,73 @@
-/*
- * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
- * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
- *
- * Licensed under the Apache License, Version 2.0 (the "License");
- * you may not use this file except in compliance with the License.
- * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
- *
- * Unless required by applicable law or agreed to in writing, software
- * distributed under the License is distributed on an "AS IS" BASIS,
- * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- * See the License for the specific language governing permissions and
- * limitations under the License.
-*/
-
-using System.Collections.Generic;
-using Newtonsoft.Json;
-using QuantConnect.Brokerages.Binance.Converters;
-using QuantConnect.Securities;
-using QuantConnect.Util;
-using Order = QuantConnect.Orders.Order;
-
-namespace QuantConnect.Brokerages.Binance
-{
-    /// <summary>
-    /// Binance Cross Margin REST API implementation
-    /// </summary>
-    public class BinanceCrossMarginRestApiClient : BinanceBaseRestApiClient
-    {
-        /// <summary>
-        /// The Api prefix
-        /// </summary>
-        /// <remarks>Depends on SPOT,MARGIN, Futures trading</remarks>
-        protected override string ApiPrefix => "/sapi/v1/margin";
-
-        /// <summary>
-        /// The websocket prefix
-        /// </summary>
-        /// <remarks>Depends on SPOT,MARGIN, Futures trading</remarks>
-        protected override string WsPrefix => "/sapi/v1";
-
-        /// <summary>
-        /// Ticker Price Change Statistics Endpoint
-        /// </summary>
-        protected override string TickerPriceChangeStatisticsEndpoint => "/api/v3/ticker/24hr";
-
-        /// <summary>
-        /// Creates a new instance
-        /// </summary>
-        public BinanceCrossMarginRestApiClient(
-            ISymbolMapper symbolMapper,
-            ISecurityProvider securityProvider,
-            string apiKey,
-            string apiSecret,
-            string restApiUrl,
-            RateGate restRateLimiter
-            )
-            : base(symbolMapper, securityProvider, apiKey, apiSecret, restApiUrl, restRateLimiter)
-        {
-        }
-
-        protected override JsonConverter CreateAccountConverter() => new MarginAccountConverter();
-
-        protected override IDictionary<string, object> CreateOrderBody(Order order)
-        {
-            var body = base.CreateOrderBody(order);
-            body["isisolated"] = "FALSE";
-            body["sideEffectType"] = "MARGIN_BUY";
-
-            return body;
-        }
-    }
-}
+/*
+ * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
+ * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
+ *
+ * Licensed under the Apache License, Version 2.0 (the "License");
+ * you may not use this file except in compliance with the License.
+ * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
+ *
+ * Unless required by applicable law or agreed to in writing, software
+ * distributed under the License is distributed on an "AS IS" BASIS,
+ * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
+ * See the License for the specific language governing permissions and
+ * limitations under the License.
+*/
+
+using System.Collections.Generic;
+using Newtonsoft.Json;
+using QuantConnect.Brokerages.Binance.Converters;
+using QuantConnect.Securities;
+using QuantConnect.Util;
+using Order = QuantConnect.Orders.Order;
+
+namespace QuantConnect.Brokerages.Binance
+{
+    /// <summary>
+    /// Binance Cross Margin REST API implementation
+    /// </summary>
+    public class BinanceCrossMarginRestApiClient : BinanceBaseRestApiClient
+    {
+        /// <summary>
+        /// The Api prefix
+        /// </summary>
+        /// <remarks>Depends on SPOT,MARGIN, Futures trading</remarks>
+        protected override string ApiPrefix => "/sapi/v1/margin";
+
+        /// <summary>
+        /// The websocket prefix
+        /// </summary>
+        /// <remarks>Depends on SPOT,MARGIN, Futures trading</remarks>
+        protected override string WsPrefix => "/sapi/v1";
+
+        /// <summary>
+        /// Ticker Price Change Statistics Endpoint
+        /// </summary>
+        protected override string TickerPriceChangeStatisticsEndpoint => "/api/v3/ticker/24hr";
+
+        /// <summary>
+        /// Creates a new instance
+        /// </summary>
+        public BinanceCrossMarginRestApiClient(
+            ISymbolMapper symbolMapper,
+            ISecurityProvider securityProvider,
+            string apiKey,
+            string apiSecret,
+            string restApiUrl,
+            RateGate restRateLimiter
+            )
+            : base(symbolMapper, securityProvider, apiKey, apiSecret, restApiUrl, restRateLimiter)
+        {
+        }
+
+        protected override JsonConverter CreateAccountConverter() => new MarginAccountConverter();
+
+        protected override IDictionary<string, object> CreateOrderBody(Order order)
+        {
+            var body = base.CreateOrderBody(order);
+            body["isisolated"] = "FALSE";
+            body["sideEffectType"] = "AUTO_BORROW_REPAY";
+
+            return body;
+        }
+    }
+}
diff --git a/QuantConnect.BinanceBrokerage/BinanceFuturesBrokerage.cs b/QuantConnect.BinanceBrokerage/BinanceFuturesBrokerage.cs
index be89265..6bb4128 100644
--- a/QuantConnect.BinanceBrokerage/BinanceFuturesBrokerage.cs
+++ b/QuantConnect.BinanceBrokerage/BinanceFuturesBrokerage.cs
@@ -90,7 +90,8 @@ namespace QuantConnect.Brokerages.Binance
             CurrencyPairUtil.DecomposeCurrencyPair(symbol, out var _, out var quoteCurrency);
 
             return quoteCurrency.Equals("USDT", System.StringComparison.InvariantCultureIgnoreCase)
-                || quoteCurrency.Equals("BUSD", System.StringComparison.InvariantCultureIgnoreCase);
+                || quoteCurrency.Equals("BUSD", System.StringComparison.InvariantCultureIgnoreCase)
+                || quoteCurrency.Equals("USDC", System.StringComparison.InvariantCultureIgnoreCase); ;
         }
 
         /// <summary>
diff --git a/QuantConnect.BinanceBrokerage/BinanceFuturesRestApiClient.cs b/QuantConnect.BinanceBrokerage/BinanceFuturesRestApiClient.cs
index 2c5ed72..4bbe0db 100644
--- a/QuantConnect.BinanceBrokerage/BinanceFuturesRestApiClient.cs
+++ b/QuantConnect.BinanceBrokerage/BinanceFuturesRestApiClient.cs
@@ -1,126 +1,128 @@
-﻿/*
- * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
- * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
- *
- * Licensed under the Apache License, Version 2.0 (the "License");
- * you may not use this file except in compliance with the License.
- * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
- *
- * Unless required by applicable law or agreed to in writing, software
- * distributed under the License is distributed on an "AS IS" BASIS,
- * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- * See the License for the specific language governing permissions and
- * limitations under the License.
-*/
-
-using Newtonsoft.Json;
-using QuantConnect.Securities;
-using QuantConnect.Brokerages.Binance.Converters;
-using System.Collections.Generic;
-using QuantConnect.Brokerages.Binance.Messages;
-using RestSharp;
-using System.Net;
-using System;
-using System.Linq;
-using QuantConnect.Util;
-
-namespace QuantConnect.Brokerages.Binance
-{
-    /// <summary>
-    /// Binance USDT Futures REST API implementation
-    /// </summary>
-    public class BinanceFuturesRestApiClient : BinanceBaseRestApiClient
-    {
-        /// <summary>
-        /// The API endpoint prefix for Binance Futures API version 1.
-        /// </summary>
-        private const string _prefix = "/fapi/v1";
-
-        /// <summary>
-        /// The API endpoint prefix for Binance Futures API version 2.
-        /// </summary>
-        private const string _prefixV2 = "/fapi/v2";
-
-        protected override JsonConverter CreateAccountConverter() => new FuturesAccountConverter();
-
-        /// <summary>
-        /// The Api prefix
-        /// </summary>
-        /// <remarks>Depends on SPOT,MARGIN, Futures trading</remarks>
-        protected override string ApiPrefix => _prefix;
-
-        /// <summary>
-        /// The websocket prefix
-        /// </summary>
-        /// <remarks>Depends on SPOT,MARGIN, Futures trading</remarks>
-        protected override string WsPrefix => _prefix;
-
-        /// <summary>
-        /// The user data stream endpoint
-        /// </summary>
-        protected override string UserDataStreamEndpoint => $"{WsPrefix}/listenKey";
-
-        protected override string GetBaseDataEndpoint() => ApiPrefix;
-
-        /// <summary>
-        /// Create a new instance
-        /// </summary>
-        public BinanceFuturesRestApiClient(
-            ISymbolMapper symbolMapper,
-            ISecurityProvider securityProvider,
-            string apiKey,
-            string apiSecret,
-            string restApiUrl,
-            RateGate restRateLimiter
-            )
-            : base(symbolMapper, securityProvider, apiKey, apiSecret, restApiUrl, restRateLimiter)
-        {
-        }
-
-        /// <summary>
-        /// Gets all open positions
-        /// </summary>
-        /// <returns>The list of all account holdings</returns>
-        public override List<Holding> GetAccountHoldings()
-        {
-            return GetAccountHoldings(_prefixV2);
-        }
-
-        public override BalanceEntry[] GetCashBalance()
-        {
-            return GetCashBalance(_prefixV2);
-        }
-
-        /// <summary>
-        /// Retrieves the current account holdings for a specified API prefix from the Binance brokerage.
-        /// </summary>
-        /// <param name="apiPrefix">
-        /// The API endpoint prefix to be used for the request, typically including the base URL and version.
-        /// </param>
-        /// <returns>
-        /// A list of <see cref="Holding"/> objects representing the current positions with non-zero amounts.
-        /// </returns>
-        protected List<Holding> GetAccountHoldings(string apiPrefix)
-        {
-            var request = new RestRequest($"{apiPrefix}/account", Method.GET);
-
-            var response = ExecuteRestRequestWithSignature(request);
-            if (response.StatusCode != HttpStatusCode.OK)
-            {
-                throw new Exception($"BinanceBrokerage.GetCashBalance: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
-            }
-
-            return JsonConvert
-                .DeserializeObject<FuturesAccountInformation>(response.Content)
-                .Positions
-                .Where(p => p.PositionAmt != 0)
-                .Select(x => new Holding
-                {
-                    Symbol = SymbolMapper.GetLeanSymbol(x.Symbol, SecurityType.CryptoFuture, Market.Binance),
-                    AveragePrice = x.EntryPrice,
-                    Quantity = x.PositionAmt,
-                })
-                .ToList();
-        }
-    }
-}
+﻿/*
+ * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
+ * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
+ *
+ * Licensed under the Apache License, Version 2.0 (the "License");
+ * you may not use this file except in compliance with the License.
+ * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
+ *
+ * Unless required by applicable law or agreed to in writing, software
+ * distributed under the License is distributed on an "AS IS" BASIS,
+ * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
+ * See the License for the specific language governing permissions and
+ * limitations under the License.
+*/
+
+using Newtonsoft.Json;
+using QuantConnect.Securities;
+using QuantConnect.Brokerages.Binance.Converters;
+using System.Collections.Generic;
+using QuantConnect.Brokerages.Binance.Messages;
+using RestSharp;
+using System.Net;
+using System;
+using System.Linq;
+using QuantConnect.Util;
+using System.Threading;
+using QuantConnect.Logging;
+
+namespace QuantConnect.Brokerages.Binance
+{
+    /// <summary>
+    /// Binance USDT Futures REST API implementation
+    /// </summary>
+    public partial class BinanceFuturesRestApiClient : BinanceBaseRestApiClient
+    {
+        /// <summary>
+        /// The API endpoint prefix for Binance Futures API version 1.
+        /// </summary>
+        private const string _prefix = "/fapi/v1";
+
+        /// <summary>
+        /// The API endpoint prefix for Binance Futures API version 2.
+        /// </summary>
+        private const string _prefixV2 = "/fapi/v2";
+
+        protected override JsonConverter CreateAccountConverter() => new FuturesAccountConverter();
+
+        /// <summary>
+        /// The Api prefix
+        /// </summary>
+        /// <remarks>Depends on SPOT,MARGIN, Futures trading</remarks>
+        protected override string ApiPrefix => _prefix;
+
+        /// <summary>
+        /// The websocket prefix
+        /// </summary>
+        /// <remarks>Depends on SPOT,MARGIN, Futures trading</remarks>
+        protected override string WsPrefix => _prefix;
+
+        /// <summary>
+        /// The user data stream endpoint
+        /// </summary>
+        protected override string UserDataStreamEndpoint => $"{WsPrefix}/listenKey";
+
+        protected override string GetBaseDataEndpoint() => ApiPrefix;
+
+        /// <summary>
+        /// Create a new instance
+        /// </summary>
+        public BinanceFuturesRestApiClient(
+            ISymbolMapper symbolMapper,
+            ISecurityProvider securityProvider,
+            string apiKey,
+            string apiSecret,
+            string restApiUrl,
+            RateGate restRateLimiter
+            )
+            : base(symbolMapper, securityProvider, apiKey, apiSecret, restApiUrl, restRateLimiter)
+        {
+        }
+
+        /// <summary>
+        /// Gets all open positions
+        /// </summary>
+        /// <returns>The list of all account holdings</returns>
+        public override List<Holding> GetAccountHoldings()
+        {
+            return GetAccountHoldings(_prefixV2);
+        }
+
+        public override BalanceEntry[] GetCashBalance()
+        {
+            return GetCashBalance(_prefixV2);
+        }
+
+        /// <summary>
+        /// Retrieves the current account holdings for a specified API prefix from the Binance brokerage.
+        /// </summary>
+        /// <param name="apiPrefix">
+        /// The API endpoint prefix to be used for the request, typically including the base URL and version.
+        /// </param>
+        /// <returns>
+        /// A list of <see cref="Holding"/> objects representing the current positions with non-zero amounts.
+        /// </returns>
+        protected List<Holding> GetAccountHoldings(string apiPrefix)
+        {
+            var request = new RestRequest($"{apiPrefix}/account", Method.GET);
+
+            var response = ExecuteRestRequestWithSignature(request);
+            if (response.StatusCode != HttpStatusCode.OK)
+            {
+                throw new Exception($"BinanceBrokerage.GetCashBalance: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
+            }
+
+            return JsonConvert
+                .DeserializeObject<FuturesAccountInformation>(response.Content)
+                .Positions
+                .Where(p => p.PositionAmt != 0)
+                .Select(x => new Holding
+                {
+                    Symbol = SymbolMapper.GetLeanSymbol(x.Symbol, SecurityType.CryptoFuture, Market.Binance),
+                    AveragePrice = x.EntryPrice,
+                    Quantity = x.PositionAmt,
+                })
+                .ToList();
+        }
+    }
+}
diff --git a/QuantConnect.BinanceBrokerage/BinanceWebSocketTradingApiClient.cs b/QuantConnect.BinanceBrokerage/BinanceWebSocketTradingApiClient.cs
new file mode 100644
index 0000000..33d46ab
--- /dev/null
+++ b/QuantConnect.BinanceBrokerage/BinanceWebSocketTradingApiClient.cs
@@ -0,0 +1,419 @@
+using System;
+using System.Collections.Concurrent;
+using System.Collections.Generic;
+using System.Globalization;
+using System.Linq;
+using System.Security.Cryptography;
+using System.Text;
+using System.Threading;
+using System.Threading.Tasks;
+using Newtonsoft.Json;
+using Newtonsoft.Json.Linq;
+using QuantConnect.Brokerages.Binance.Messages;
+using QuantConnect.Logging;
+using QuantConnect.Orders;
+using QuantConnect.Orders.Fees;
+using QuantConnect.Securities;
+using QuantConnect.Util;
+
+namespace QuantConnect.Brokerages.Binance {
+  public partial class BinanceFuturesRestApiClient : BinanceBaseRestApiClient {
+    public static bool UseWebSocketApiForTrading = true;
+    
+    // Configurable timeout for WebSocket requests
+    private const int DefaultWebSocketTimeoutMs = 30000;
+    private const int WebSocketConnectionTimeoutMs = 5000;
+
+    private WebSocketClientWrapper _tradingWebSocket;
+    private readonly ConcurrentDictionary<string, TaskCompletionSource<JObject>> _pendingRequests = new();
+    private readonly object _webSocketLock = new object();
+    private long _requestIdCounter = 0;
+    private Timer _websocketKeepAliveTimer;
+    private bool _isWebSocketConnected = false;
+    private string _tradingWebSocketUrl = "wss://ws-fapi.binance.com/ws-fapi/v1";
+
+    private static string ConvertOrderDirection(OrderDirection orderDirection) {
+      if (orderDirection == OrderDirection.Buy || orderDirection == OrderDirection.Sell) {
+        return orderDirection.ToString().ToUpperInvariant();
+      }
+
+      throw new NotSupportedException($"BinanceWebSocketTradingApi.ConvertOrderDirection: Unsupported order direction: {orderDirection}");
+    }
+
+    private void InitializeTradingWebSocket() {
+      if (!UseWebSocketApiForTrading || _tradingWebSocket != null)
+        return;
+
+      lock (_webSocketLock) {
+        if (_tradingWebSocket != null)
+          return;
+
+        try {
+          Log.Trace($"BinanceWebSocketTradingApi.InitializeTradingWebSocket(): Initializing trading WebSocket at {_tradingWebSocketUrl}");
+
+          _tradingWebSocket = new WebSocketClientWrapper();
+
+          _tradingWebSocket.Initialize(_tradingWebSocketUrl, ApiKey);
+          _tradingWebSocket.Message += OnTradingWebSocketMessage;
+          _tradingWebSocket.Error += OnTradingWebSocketError;
+          _tradingWebSocket.Open += OnTradingWebSocketOpen;
+          _tradingWebSocket.Closed += OnTradingWebSocketClosed;
+
+          _tradingWebSocket.Connect();
+
+          //_websocketKeepAliveTimer = new Timer(SendTradingWebSocketPing, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
+        } catch (Exception ex) {
+          Log.Error($"BinanceWebSocketTradingApi.InitializeTradingWebSocket(): Failed to initialize trading WebSocket: {ex.Message}");
+          throw;
+        }
+      }
+    }
+
+    private void OnTradingWebSocketOpen(object sender, EventArgs e) {
+      _isWebSocketConnected = true;
+      Log.Trace("BinanceWebSocketTradingApi.OnTradingWebSocketOpen(): Trading WebSocket connection opened successfully");
+    }
+
+    private void OnTradingWebSocketMessage(object sender, WebSocketMessage e) {
+      try {
+        var textMessage = e.Data as WebSocketClientWrapper.TextMessage;
+        if (textMessage == null) return;
+
+        if (true/*Log.DebuggingEnabled*/) {
+          Log.Trace($"BinanceWebSocketTradingApi.OnTradingWebSocketMessage(): Received message: {textMessage.Message}");
+        }
+
+        var response = JObject.Parse(textMessage.Message);
+        var id = response["id"]?.ToString();
+
+        if (!string.IsNullOrEmpty(id) && _pendingRequests.TryRemove(id, out var taskSource)) {
+          taskSource.SetResult(response);
+        } else {
+          Log.Trace($"BinanceWebSocketTradingApi.OnTradingWebSocketMessage(): Received message without matching request ID: {id}");
+        }
+      } catch (Exception ex) {
+        Log.Error($"BinanceWebSocketTradingApi.OnTradingWebSocketMessage(): Error processing WebSocket message: {ex.Message}");
+      }
+    }
+
+    private void OnTradingWebSocketError(object sender, WebSocketError e) {
+      _isWebSocketConnected = false;
+      Log.Error($"BinanceWebSocketTradingApi.OnTradingWebSocketError(): Trading WebSocket error: {e.Message}");
+
+      foreach (var kvp in _pendingRequests.ToArray()) {
+        if (_pendingRequests.TryRemove(kvp.Key, out var taskSource)) {
+          taskSource.SetException(new Exception($"WebSocket error: {e.Message}"));
+        }
+      }
+    }
+
+    private void OnTradingWebSocketClosed(object sender, WebSocketCloseData e) {
+      _isWebSocketConnected = false;
+      Log.Trace($"BinanceWebSocketTradingApi.OnTradingWebSocketClosed(): Trading WebSocket connection closed. Code: {e.Code}, Reason: {e.Reason}");
+
+      if (e.Code != 1000 && UseWebSocketApiForTrading) {
+        Task.Delay(5000).ContinueWith(_ => ReconnectTradingWebSocket());
+      }
+    }
+
+    private void ReconnectTradingWebSocket() {
+      if (!UseWebSocketApiForTrading)
+        return;
+
+      lock (_webSocketLock) {
+        try {
+          Log.Trace("BinanceWebSocketTradingApi.ReconnectTradingWebSocket(): Attempting to reconnect trading WebSocket");
+          _tradingWebSocket?.Close();
+          _tradingWebSocket = null;
+          _isWebSocketConnected = false;
+
+          InitializeTradingWebSocket();
+        } catch (Exception ex) {
+          Log.Error($"BinanceWebSocketTradingApi.ReconnectTradingWebSocket(): Failed to reconnect: {ex.Message}");
+        }
+      }
+    }
+
+    private void SendTradingWebSocketPing(object state) {
+      if (_isWebSocketConnected && _tradingWebSocket?.IsOpen == true) {
+        try {
+
+          var pingMessage = new { method = "ping" };
+          var pingJson = JsonConvert.SerializeObject(pingMessage);
+          _tradingWebSocket.Send(pingJson);
+          Log.Trace("BinanceWebSocketTradingApi.SendTradingWebSocketPing(): Sent ping to trading WebSocket");
+        } catch (Exception ex) {
+          Log.Error($"BinanceWebSocketTradingApi.SendTradingWebSocketPing(): Failed to send ping: {ex.Message}");
+        }
+      }
+    }
+
+    private string GenerateWebSocketSignature(string queryString) {
+      var keyBytes = Encoding.UTF8.GetBytes(ApiSecret);
+      var dataBytes = Encoding.UTF8.GetBytes(queryString);
+
+      using (var hmacsha256 = new HMACSHA256(keyBytes)) {
+        var hash = hmacsha256.ComputeHash(dataBytes);
+        return BitConverter.ToString(hash).Replace("-", "").ToLower();
+      }
+    }
+
+    private async Task<JObject> SendWebSocketRequestAsync(string method, Dictionary<string, object> parameters, int timeoutMs = DefaultWebSocketTimeoutMs) {
+      if (!_isWebSocketConnected || _tradingWebSocket?.IsOpen != true) {
+        Log.Trace($"BinanceWebSocketTradingApi.SendWebSocketRequestAsync(): WebSocket not connected, attempting to reconnect");
+        
+        try {
+          if (_tradingWebSocket != null) {
+            try {
+              _tradingWebSocket.Close();
+            } catch (Exception) {
+            }
+            _tradingWebSocket = null;
+          }
+          
+          InitializeTradingWebSocket();
+          
+          var connectionWait = 0;
+          while (!_isWebSocketConnected && connectionWait < WebSocketConnectionTimeoutMs) {
+            await Task.Delay(50);
+            connectionWait += 50;
+            
+            if (_tradingWebSocket == null) {
+              throw new Exception("WebSocket initialization failed");
+            }
+          }
+
+          if (!_isWebSocketConnected) {
+            throw new Exception("Trading WebSocket failed to connect after reconnection attempt");
+          }
+        } catch (Exception ex) {
+          Log.Error($"BinanceWebSocketTradingApi.SendWebSocketRequestAsync(): Failed to reconnect WebSocket: {ex.Message}");
+          throw;
+        }
+      }
+
+      var requestId = Interlocked.Increment(ref _requestIdCounter).ToString();
+      var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
+
+      parameters["apiKey"] = ApiKey;
+      parameters["timestamp"] = timestamp;
+
+      var queryParams = parameters
+          .OrderBy(kvp => kvp.Key)
+          .Select(kvp => $"{kvp.Key}={kvp.Value}")
+          .ToArray();
+      var queryString = string.Join("&", queryParams);
+
+      var signature = GenerateWebSocketSignature(queryString);
+      parameters["signature"] = signature;
+
+      var request = new {
+        id = requestId,
+        method = method,
+        @params = parameters
+      };
+
+      var requestJson = JsonConvert.SerializeObject(request);
+      Log.Trace($"BinanceWebSocketTradingApi.SendWebSocketRequestAsync(): Sending {method} request with ID {requestId}");
+
+      if (Log.DebuggingEnabled) {
+        Log.Trace($"BinanceWebSocketTradingApi.SendWebSocketRequestAsync(): Request method: {method}, params count: {parameters.Count}");
+      }
+
+      var taskSource = new TaskCompletionSource<JObject>();
+      _pendingRequests[requestId] = taskSource;
+
+      try {
+        _tradingWebSocket.Send(requestJson);
+
+        using (var cts = new CancellationTokenSource(timeoutMs)) {
+          cts.Token.Register(() => taskSource.TrySetException(new TimeoutException("WebSocket request timeout")));
+          return await taskSource.Task;
+        }
+      } catch (Exception) {
+        _pendingRequests.TryRemove(requestId, out _);
+        throw;
+      }
+    }
+
+    private async Task<bool> PlaceOrderWebSocketAsync(QuantConnect.Orders.Order order) {
+      try {
+        Log.Trace($"BinanceWebSocketTradingApi.PlaceOrderWebSocketAsync(): Placing order {order.Id} via WebSocket API");
+
+        var parameters = CreateWebSocketOrderParameters(order);
+        var response = await SendWebSocketRequestAsync("order.place", parameters);
+
+        var status = response["status"]?.ToObject<int>();
+        if (status == 200) {
+          var result = response["result"];
+          if (result != null) {
+
+            var orderResponse = ConvertWebSocketOrderResponse(result, order);
+            OnOrderSubmit(orderResponse, order);
+            
+            Log.Trace($"BinanceWebSocketTradingApi.PlaceOrderWebSocketAsync(): Successfully placed order {order.Id} via WebSocket");
+            return true;
+          }
+        }
+
+        var errorMsg = $"WebSocket order placement failed. Status: {status}, Response: {response}";
+        Log.Error($"BinanceWebSocketTradingApi.PlaceOrderWebSocketAsync(): {errorMsg}");
+
+        OnOrderEvent(new OrderEvent(order, DateTime.UtcNow, OrderFee.Zero, "Binance WebSocket Order Event") {
+          Status = OrderStatus.Invalid,
+          Message = errorMsg
+        });
+
+        return false;
+      } catch (Exception ex) {
+        Log.Error($"BinanceWebSocketTradingApi.PlaceOrderWebSocketAsync(): Exception placing order {order.Id}: {ex.Message}");
+
+        OnOrderEvent(new OrderEvent(order, DateTime.UtcNow, OrderFee.Zero, "Binance WebSocket Order Event") {
+          Status = OrderStatus.Invalid,
+          Message = ex.Message
+        });
+
+        return false;
+      }
+    }
+
+    private async Task<bool> CancelOrderWebSocketAsync(QuantConnect.Orders.Order order) {
+      try {
+        Log.Trace($"BinanceWebSocketTradingApi.CancelOrderWebSocketAsync(): Cancelling order {order.Id} via WebSocket API");
+
+        var allSuccess = true;
+        foreach (var brokerId in order.BrokerId) {
+          var parameters = new Dictionary<string, object> {
+            ["symbol"] = SymbolMapper.GetBrokerageSymbol(order.Symbol),
+            ["orderId"] = brokerId
+          };
+
+          var response = await SendWebSocketRequestAsync("order.cancel", parameters);
+          var status = response["status"]?.ToObject<int>();
+
+          if (status != 200) {
+            allSuccess = false;
+            Log.Error($"BinanceWebSocketTradingApi.CancelOrderWebSocketAsync(): Failed to cancel order {order.Id}, broker ID {brokerId}. Status: {status}");
+          } else {
+            Log.Trace($"BinanceWebSocketTradingApi.CancelOrderWebSocketAsync(): Successfully cancelled order {order.Id}, broker ID {brokerId} via WebSocket");
+          }
+        }
+
+        if (allSuccess) {
+          OnOrderEvent(new OrderEvent(order, DateTime.UtcNow, OrderFee.Zero, "Binance WebSocket Order Event") {
+            Status = OrderStatus.Canceled
+          });
+        }
+
+        return allSuccess;
+      } catch (Exception ex) {
+        Log.Error($"BinanceWebSocketTradingApi.CancelOrderWebSocketAsync(): Exception cancelling order {order.Id}: {ex.Message}");
+        return false;
+      }
+    }
+
+    private Dictionary<string, object> CreateWebSocketOrderParameters(QuantConnect.Orders.Order order) {
+      var parameters = new Dictionary<string, object> {
+        ["symbol"] = SymbolMapper.GetBrokerageSymbol(order.Symbol),
+        ["side"] = ConvertOrderDirection(order.Direction),
+        ["quantity"] = Math.Abs(order.Quantity).ToString(CultureInfo.InvariantCulture)
+      };
+
+      parameters["positionSide"] = "BOTH";
+
+      switch (order) {
+        case LimitOrder limitOrder:
+          parameters["type"] = "LIMIT";
+          parameters["timeInForce"] = "GTX";
+          parameters["price"] = limitOrder.LimitPrice.ToString(CultureInfo.InvariantCulture);
+          break;
+
+        case StopLimitOrder stopLimitOrder:
+          var tickerPrice = GetTickerPrice(order);
+          var stopPrice = stopLimitOrder.StopPrice;
+          parameters["type"] = (order.Direction == OrderDirection.Sell) == (stopPrice <= tickerPrice) ? "STOP" : "TAKE_PROFIT";
+
+          parameters["timeInForce"] = "GTX";
+          parameters["stopPrice"] = stopPrice.ToString(CultureInfo.InvariantCulture);
+          parameters["price"] = stopLimitOrder.LimitPrice.ToString(CultureInfo.InvariantCulture);
+          break;
+
+        case MarketOrder:
+          parameters["type"] = "MARKET";
+          break;
+
+        case StopMarketOrder stopMarketOrder:
+          parameters["type"] = "STOP_MARKET";
+          parameters["stopPrice"] = stopMarketOrder.StopPrice.ToString(CultureInfo.InvariantCulture);
+          break;
+
+        default:
+          throw new NotSupportedException($"BinanceWebSocketTradingApi.CreateWebSocketOrderParameters(): Unsupported order type: {order.Type}");
+      }
+
+      Log.Trace($"BinanceWebSocketTradingApi.CreateWebSocketOrderParameters(): Created parameters for {order.Type} order {order.Id}");
+      return parameters;
+    }
+
+    private NewOrder ConvertWebSocketOrderResponse(JToken result, QuantConnect.Orders.Order order) {
+      return new NewOrder {
+        Id = result["orderId"]?.ToString(),
+        Symbol = result["symbol"]?.ToString(),
+        Status = result["status"]?.ToString(),
+        Type = result["type"]?.ToString(),
+        Side = result["side"]?.ToString(),
+        OriginalAmount = result["origQty"]?.ToObject<decimal>() ?? 0,
+        ExecutedAmount = result["executedQty"]?.ToObject<decimal>() ?? 0,
+        Price = result["price"]?.ToObject<decimal>() ?? 0,
+        StopPrice= result["stopPrice"]?.ToObject<decimal>() ?? 0,
+        TransactionTime = result["updateTime"]?.ToObject<long>() ?? DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
+      };
+    }
+
+    public override bool PlaceOrder(QuantConnect.Orders.Order order) {
+      if (UseWebSocketApiForTrading) {
+        Log.Trace($"BinanceWebSocketTradingApi.PlaceOrder(): Using WebSocket API for order {order.Id}");
+
+        try {
+          var task = PlaceOrderWebSocketAsync(order);
+          if (task.Wait(DefaultWebSocketTimeoutMs)) {
+            return task.Result;
+          } else {
+            Log.Error($"BinanceWebSocketTradingApi.PlaceOrder(): WebSocket order placement timed out for order {order.Id}");
+            return base.PlaceOrder(order);
+          }
+        } catch (Exception ex) {
+          Log.Error($"BinanceWebSocketTradingApi.PlaceOrder(): WebSocket order placement failed for order {order.Id}, falling back to REST API: {ex.Message}");
+
+          return base.PlaceOrder(order);
+        }
+      } else {
+        Log.Trace($"BinanceWebSocketTradingApi.PlaceOrder(): Using REST API for order {order.Id}");
+        return base.PlaceOrder(order);
+      }
+    }
+
+    public override bool CancelOrder(QuantConnect.Orders.Order order) {
+      if (UseWebSocketApiForTrading) {
+        Log.Trace($"BinanceWebSocketTradingApi.CancelOrder(): Using WebSocket API for order {order.Id}");
+
+        try {
+          var task = CancelOrderWebSocketAsync(order);
+          if (task.Wait(DefaultWebSocketTimeoutMs)) {
+            return task.Result;
+          } else {
+            Log.Error($"BinanceWebSocketTradingApi.CancelOrder(): WebSocket order cancellation timed out for order {order.Id}");
+            return base.CancelOrder(order);
+          }
+        } catch (Exception ex) {
+          Log.Error($"BinanceWebSocketTradingApi.CancelOrder(): WebSocket order cancellation failed for order {order.Id}, falling back to REST API: {ex.Message}");
+
+          return base.CancelOrder(order);
+        }
+      } else {
+        Log.Trace($"BinanceWebSocketTradingApi.CancelOrder(): Using REST API for order {order.Id}");
+        return base.CancelOrder(order);
+      }
+    }
+  }
+}
\ No newline at end of file
diff --git a/QuantConnect.BinanceBrokerage/Messages/Execution.cs b/QuantConnect.BinanceBrokerage/Messages/Execution.cs
index 6d60da0..16a09c8 100644
--- a/QuantConnect.BinanceBrokerage/Messages/Execution.cs
+++ b/QuantConnect.BinanceBrokerage/Messages/Execution.cs
@@ -1,65 +1,65 @@
-/*
- * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
- * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
- *
- * Licensed under the Apache License, Version 2.0 (the "License");
- * you may not use this file except in compliance with the License.
- * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
- *
- * Unless required by applicable law or agreed to in writing, software
- * distributed under the License is distributed on an "AS IS" BASIS,
- * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- * See the License for the specific language governing permissions and
- * limitations under the License.
-*/
-
-using Newtonsoft.Json;
-using QuantConnect.Orders;
-using System;
-
-namespace QuantConnect.Brokerages.Binance.Messages
-{
-#pragma warning disable 1591
-
-    public class Execution : BaseMessage
-    {
-        public override EventType @Event => EventType.Execution;
-
-        [JsonProperty("i")]
-        public string OrderId { get; set; }
-
-        [JsonProperty("t")]
-        public string TradeId { get; set; }
-
-        [JsonProperty("I")]
-        public string Ignore { get; set; }
-
-        [JsonProperty("x")]
-        public string ExecutionType { get; private set; }
-
-        [JsonProperty("X")]
-        public string OrderStatus { get; private set; }
-
-        [JsonProperty("T")]
-        public long TransactionTime { get; set; }
-
-        [JsonProperty("L")]
-        public decimal LastExecutedPrice { get; set; }
-
-        [JsonProperty("l")]
-        public decimal LastExecutedQuantity { get; set; }
-
-        [JsonProperty("S")]
-        public string Side { get; set; }
-
-        [JsonProperty("n")]
-        public decimal Fee { get; set; }
-
-        [JsonProperty("N")]
-        public string FeeCurrency { get; set; }
-
-        public OrderDirection Direction => Side.Equals("BUY", StringComparison.OrdinalIgnoreCase) ? OrderDirection.Buy : OrderDirection.Sell;
-    }
-
-#pragma warning restore 1591
-}
+/*
+ * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
+ * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
+ *
+ * Licensed under the Apache License, Version 2.0 (the "License");
+ * you may not use this file except in compliance with the License.
+ * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
+ *
+ * Unless required by applicable law or agreed to in writing, software
+ * distributed under the License is distributed on an "AS IS" BASIS,
+ * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
+ * See the License for the specific language governing permissions and
+ * limitations under the License.
+*/
+
+using Newtonsoft.Json;
+using QuantConnect.Orders;
+using System;
+
+namespace QuantConnect.Brokerages.Binance.Messages
+{
+#pragma warning disable 1591
+
+    public class Execution : BaseMessage
+    {
+        public override EventType @Event => EventType.Execution;
+
+        [JsonProperty("i")]
+        public string OrderId { get; set; }
+
+        [JsonProperty("t")]
+        public string TradeId { get; set; }
+
+        [JsonProperty("I")]
+        public string Ignore { get; set; }
+
+        [JsonProperty("x")]
+        public string ExecutionType { get; private set; }
+
+        [JsonProperty("X")]
+        public string OrderStatus { get; set; }
+
+        [JsonProperty("T")]
+        public long TransactionTime { get; set; }
+
+        [JsonProperty("L")]
+        public decimal LastExecutedPrice { get; set; }
+
+        [JsonProperty("l")]
+        public decimal LastExecutedQuantity { get; set; }
+
+        [JsonProperty("S")]
+        public string Side { get; set; }
+
+        [JsonProperty("n")]
+        public decimal Fee { get; set; }
+
+        [JsonProperty("N")]
+        public string FeeCurrency { get; set; }
+
+        public OrderDirection Direction => Side.Equals("BUY", StringComparison.OrdinalIgnoreCase) ? OrderDirection.Buy : OrderDirection.Sell;
+    }
+
+#pragma warning restore 1591
+}
diff --git a/QuantConnect.BinanceBrokerage/QuantConnect.BinanceBrokerage.csproj b/QuantConnect.BinanceBrokerage/QuantConnect.BinanceBrokerage.csproj
index 0e882ab..0ecce4d 100644
--- a/QuantConnect.BinanceBrokerage/QuantConnect.BinanceBrokerage.csproj
+++ b/QuantConnect.BinanceBrokerage/QuantConnect.BinanceBrokerage.csproj
@@ -1,32 +1,35 @@
-﻿<Project Sdk="Microsoft.NET.Sdk">
-  <PropertyGroup>
-    <Configuration Condition=" '$(Configuration)' == '' ">Release</Configuration>
-    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
-    <TargetFramework>net9.0</TargetFramework>
-    <Product>QuantConnect.Brokerages.Binance</Product>
-    <AssemblyName>QuantConnect.Brokerages.Binance</AssemblyName>
-    <RootNamespace>QuantConnect.Brokerages.Binance</RootNamespace>
-    <AssemblyTitle>QuantConnect.Brokerages.Binance</AssemblyTitle>
-    <OutputType>Library</OutputType>
-    <OutputPath>bin\$(Configuration)\</OutputPath>
-    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
-    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
-    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
-    <Description>QuantConnect LEAN Binance Brokerage: Brokerage Binance plugin for Lean</Description>
-  </PropertyGroup>
-  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
-    <DebugType>full</DebugType>
-    <OutputPath>bin\Debug\</OutputPath>
-  </PropertyGroup>
-  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
-    <DebugType>pdbonly</DebugType>
-    <OutputPath>bin\Release\</OutputPath>
-  </PropertyGroup>
-  <ItemGroup>
-    <Compile Include="..\..\Lean\Common\Properties\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
-  </ItemGroup>
-  <ItemGroup>
-    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
-    <PackageReference Include="QuantConnect.Brokerages" Version="2.5.*" />
-  </ItemGroup>
+﻿<Project Sdk="Microsoft.NET.Sdk">
+  <PropertyGroup>
+    <Configuration Condition=" '$(Configuration)' == '' ">Release</Configuration>
+    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
+    <TargetFramework>net9.0</TargetFramework>
+    <Product>QuantConnect.Brokerages.Binance</Product>
+    <AssemblyName>QuantConnect.Brokerages.Binance</AssemblyName>
+    <RootNamespace>QuantConnect.Brokerages.Binance</RootNamespace>
+    <AssemblyTitle>QuantConnect.Brokerages.Binance</AssemblyTitle>
+    <OutputType>Library</OutputType>
+    <OutputPath>bin\$(Configuration)\</OutputPath>
+    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
+    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
+    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
+    <Description>QuantConnect LEAN Binance Brokerage: Brokerage Binance plugin for Lean</Description>
+  </PropertyGroup>
+  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
+    <DebugType>full</DebugType>
+    <OutputPath>bin\Debug\</OutputPath>
+  </PropertyGroup>
+  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
+    <DebugType>pdbonly</DebugType>
+    <OutputPath>bin\Release\</OutputPath>
+  </PropertyGroup>
+  <ItemGroup>
+    <Compile Include="..\..\Lean\Common\Properties\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
+  </ItemGroup>
+  <ItemGroup>
+    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
+    <PackageReference Include="QuantConnect.Brokerages" Version="2.5.*" />
+  </ItemGroup>
+  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
+    <Exec Command="xcopy D:\work\Lean.Brokerages.Binance\QuantConnect.BinanceBrokerage\bin\Debug\QuantConnect.Brokerages.Binance.dll D:\work\Lean\Launcher\bin\Debug /E /Y /I /F&#xD;&#xA;xcopy D:\work\Lean.Brokerages.Binance\QuantConnect.BinanceBrokerage\bin\Release\QuantConnect.Brokerages.Binance.dll D:\work\Lean\Launcher\bin\Release /E /Y /I /F&#xD;&#xA;&#xD;&#xA;del /q &quot;D:\work\xstarwalker168\Python\Finance\QuantConnectLean\QC-Log-Dir\*&quot;&#xD;&#xA;" />
+  </Target>
 </Project>
\ No newline at end of file

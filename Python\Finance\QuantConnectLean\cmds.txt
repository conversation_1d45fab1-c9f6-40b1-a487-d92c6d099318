1. parepare python
  # only support python 3.11
    https://github.com/QuantConnect/Lean/tree/master/Algorithm.Python#quantconnect-python-algorithm-project

    Win:
      choco install python311 -y -v
      py -3.11 -m pip install lean
      PYTHONNET_PYDLL: C:\Python311\python311.dll
      dotnet build ""D:\work\Lean.Brokerages.Binance\QuantConnect.BinanceBrokerage\QuantConnect.BinanceBrokerage.csproj"" --configuration Debug
      dotnet build "D:\work\xstarwalker168\Python\Finance\QuantConnectLean\CryptoBot(C#)\cCryptoBot\cCryptoBot.csproj" --configuration Debug
    
    Ubuntu:
      # 1. Python
        # install python
          export PATH="$HOME/miniconda3/bin:$PATH"
          wget https://cdn.quantconnect.com/miniconda/Miniconda3-py311_24.9.2-0-Linux-x86_64.sh
          bash Miniconda3-py311_24.9.2-0-Linux-x86_64.sh -b -p /opt/miniconda3
          rm -rf Miniconda3-py311_24.9.2-0-Linux-x86_64.sh
        
        # Create a new Python environment with the needed dependencies
          /opt/miniconda3/bin/conda create -n qc_lean python=3.11.11 pandas=2.1.4 wrapt=1.17.0
          /opt/miniconda3/bin/conda init ## must start a new terminal
          /opt/miniconda3/bin/conda activate qc_lean

          (base) root@C20240728121274:/opt/miniconda3/bin# python -m pip install lean, numpy

        # Set PYTHONNET_PYDLL
          /etc/environment # open with vs-code
          PYTHONNET_PYDLL="/opt/miniconda3/envs/qc_lean/lib/libpython3.11.so"

        # restart terminal # necessary

      # 2. dotnet9
        # a. install dotnet9
          # Ubuntu 24.04 LTS
            sudo add-apt-repository ppa:dotnet/backports
            sudo apt-get update && sudo apt-get install -y dotnet-sdk-9.0
          # Ubuntu 24.10
            sudo apt-get update && sudo apt-get install -y dotnet-sdk-9.0

      # 3. run
        cd /root/xQuantConnect/bin

        cd /root/xQuantConnect/bin && rm -rf ./*
        cd /root/xQuantConnect/bin && unzip Release.zip -d .

        cd /root/xQuantConnect/bin && screen -d -m dotnet /root/xQuantConnect/bin/QuantConnect.Lean.Launcher.dll --config /root/xQuantConnect/trading-bot-config.json --parameters tradeMode:1,symbol:ETH,currency:USDC,leverage:10 --results-destination-folder /root/xQuantConnect/QC-Log-Dir --algorithm-language CSharp --environment live-futures-binance --algorithm-location /root/xQuantConnect/bin/cCryptoBot.dll --data-folder /root/xQuantConnect/Data

        screen -X -S $(screen -ls | grep Detached | awk '{print $1}' | cut -d. -f1) quit



2. parepare BinanceBrokerage
  xcopy D:\work\Lean.Brokerages.Binance\QuantConnect.BinanceBrokerage\bin\Debug\QuantConnect.Brokerages.Binance.dll D:\work\Lean\Launcher\bin\Debug /E /Y /I /F

  xcopy D:\work\Lean.Brokerages.Binance\QuantConnect.BinanceBrokerage\bin\Release\QuantConnect.Brokerages.Binance.dll D:\work\Lean\Launcher\bin\Release /E /Y /I /F

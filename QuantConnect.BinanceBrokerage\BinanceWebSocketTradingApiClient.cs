using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using QuantConnect.Brokerages.Binance.Messages;
using QuantConnect.Logging;
using QuantConnect.Orders;
using QuantConnect.Orders.Fees;
using QuantConnect.Securities;
using QuantConnect.Util;

namespace QuantConnect.Brokerages.Binance {
  public partial class BinanceFuturesRestApiClient : BinanceBaseRestApiClient {
    public static bool UseWebSocketApiForTrading = true;

    // Configurable timeout for WebSocket requests
    private const int DefaultWebSocketTimeoutMs = 30000;
    private const int WebSocketConnectionTimeoutMs = 5000;

    private WebSocketClientWrapper _tradingWebSocket;
    private readonly ConcurrentDictionary<string, TaskCompletionSource<JObject>> _pendingRequests = new();
    private readonly object _webSocketLock = new object();
    private long _requestIdCounter = 0;
    private Timer _websocketKeepAliveTimer;
    private bool _isWebSocketConnected = false;
    private string _tradingWebSocketUrl = "wss://ws-fapi.binance.com/ws-fapi/v1";

    private static string ConvertOrderDirection(OrderDirection orderDirection) {
      if (orderDirection == OrderDirection.Buy || orderDirection == OrderDirection.Sell) {
        return orderDirection.ToString().ToUpperInvariant();
      }

      throw new NotSupportedException($"BinanceWebSocketTradingApi.ConvertOrderDirection: Unsupported order direction: {orderDirection}");
    }

    private void InitializeTradingWebSocket() {
      if (!UseWebSocketApiForTrading || _tradingWebSocket != null)
        return;

      lock (_webSocketLock) {
        if (_tradingWebSocket != null)
          return;

        try {
          Log.Trace($"BinanceWebSocketTradingApi.InitializeTradingWebSocket(): Initializing trading WebSocket at {_tradingWebSocketUrl}");

          _tradingWebSocket = new WebSocketClientWrapper();

          _tradingWebSocket.Initialize(_tradingWebSocketUrl, ApiKey);
          _tradingWebSocket.Message += OnTradingWebSocketMessage;
          _tradingWebSocket.Error += OnTradingWebSocketError;
          _tradingWebSocket.Open += OnTradingWebSocketOpen;
          _tradingWebSocket.Closed += OnTradingWebSocketClosed;

          _tradingWebSocket.Connect();

        } catch (Exception ex) {
          Log.Error($"BinanceWebSocketTradingApi.InitializeTradingWebSocket(): Failed to initialize trading WebSocket: {ex.Message}");
          throw;
        }
      }
    }

    private void OnTradingWebSocketOpen(object sender, EventArgs e) {
      _isWebSocketConnected = true;
      Log.Trace("BinanceWebSocketTradingApi.OnTradingWebSocketOpen(): Trading WebSocket connection opened successfully");
    }

    private void OnTradingWebSocketMessage(object sender, WebSocketMessage e) {
      try {
        var textMessage = e.Data as WebSocketClientWrapper.TextMessage;
        if (textMessage == null) return;

        if (true/*Log.DebuggingEnabled*/) {
          Log.Trace($"BinanceWebSocketTradingApi.OnTradingWebSocketMessage(): Received message: {textMessage.Message}");
        }

        var response = JObject.Parse(textMessage.Message);
        var method = response["method"]?.ToString();

        if (method == "ping") {
          var pong = new JObject {
            ["method"] = "pong"
          };
          if (response["params"] != null) {
            pong["params"] = response["params"];
          }
          _tradingWebSocket.Send(pong.ToString(Formatting.None));
          return;
        }

        var id = response["id"]?.ToString();
        if (!string.IsNullOrEmpty(id) && _pendingRequests.TryRemove(id, out var taskSource)) {
          taskSource.SetResult(response);
        } else {
          Log.Trace($"BinanceWebSocketTradingApi.OnTradingWebSocketMessage(): Received message without matching request ID: {id}");
        }
      } catch (Exception ex) {
        Log.Error($"BinanceWebSocketTradingApi.OnTradingWebSocketMessage(): Error processing WebSocket message: {ex.Message}");
      }
    }

    private void OnTradingWebSocketError(object sender, WebSocketError e) {
      _isWebSocketConnected = false;
      Log.Error($"BinanceWebSocketTradingApi.OnTradingWebSocketError(): Trading WebSocket error: {e.Message}");

      foreach (var kvp in _pendingRequests.ToArray()) {
        if (_pendingRequests.TryRemove(kvp.Key, out var taskSource)) {
          taskSource.SetException(new Exception($"WebSocket error: {e.Message}"));
        }
      }
    }

    private void OnTradingWebSocketClosed(object sender, WebSocketCloseData e) {
      _isWebSocketConnected = false;
      Log.Trace($"BinanceWebSocketTradingApi.OnTradingWebSocketClosed(): Trading WebSocket connection closed. Code: {e.Code}, Reason: {e.Reason}");

      if (e.Code != 1000 && UseWebSocketApiForTrading) {
        Task.Delay(5000).ContinueWith(_ => ReconnectTradingWebSocket());
      }
    }

    private void ReconnectTradingWebSocket() {
      if (!UseWebSocketApiForTrading)
        return;

      lock (_webSocketLock) {
        try {
          Log.Trace("BinanceWebSocketTradingApi.ReconnectTradingWebSocket(): Attempting to reconnect trading WebSocket");
          _tradingWebSocket?.Close();
          _tradingWebSocket = null;
          _isWebSocketConnected = false;

          InitializeTradingWebSocket();
        } catch (Exception ex) {
          Log.Error($"BinanceWebSocketTradingApi.ReconnectTradingWebSocket(): Failed to reconnect: {ex.Message}");
        }
      }
    }

    private string GenerateWebSocketSignature(string queryString) {
      var keyBytes = Encoding.UTF8.GetBytes(ApiSecret);
      var dataBytes = Encoding.UTF8.GetBytes(queryString);

      using (var hmacsha256 = new HMACSHA256(keyBytes)) {
        var hash = hmacsha256.ComputeHash(dataBytes);
        return BitConverter.ToString(hash).Replace("-", "").ToLower();
      }
    }

    private async Task<JObject> SendWebSocketRequestAsync(string method, Dictionary<string, object> parameters, int timeoutMs = DefaultWebSocketTimeoutMs) {
      if (!_isWebSocketConnected || _tradingWebSocket?.IsOpen != true) {
        Log.Trace($"BinanceWebSocketTradingApi.SendWebSocketRequestAsync(): WebSocket not connected, attempting to reconnect");

        try {
          if (_tradingWebSocket != null) {
            try {
              _tradingWebSocket.Close();
            } catch (Exception) {
            }
            _tradingWebSocket = null;
          }

          InitializeTradingWebSocket();

          var connectionWait = 0;
          while (!_isWebSocketConnected && connectionWait < WebSocketConnectionTimeoutMs) {
            await Task.Delay(50);
            connectionWait += 50;

            if (_tradingWebSocket == null) {
              throw new Exception("WebSocket initialization failed");
            }
          }

          if (!_isWebSocketConnected) {
            throw new Exception("Trading WebSocket failed to connect after reconnection attempt");
          }
        } catch (Exception ex) {
          Log.Error($"BinanceWebSocketTradingApi.SendWebSocketRequestAsync(): Failed to reconnect WebSocket: {ex.Message}");
          throw;
        }
      }

      var requestId = Interlocked.Increment(ref _requestIdCounter).ToString();
      var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

      parameters["apiKey"] = ApiKey;
      parameters["timestamp"] = timestamp;

      var queryParams = parameters
          .OrderBy(kvp => kvp.Key)
          .Select(kvp => $"{kvp.Key}={kvp.Value}")
          .ToArray();
      var queryString = string.Join("&", queryParams);

      var signature = GenerateWebSocketSignature(queryString);
      parameters["signature"] = signature;

      var request = new {
        id = requestId,
        method = method,
        @params = parameters
      };

      var requestJson = JsonConvert.SerializeObject(request);
      Log.Trace($"BinanceWebSocketTradingApi.SendWebSocketRequestAsync(): Sending {method} request with ID {requestId}");

      if (Log.DebuggingEnabled) {
        Log.Trace($"BinanceWebSocketTradingApi.SendWebSocketRequestAsync(): Request method: {method}, params count: {parameters.Count}");
      }

      var taskSource = new TaskCompletionSource<JObject>();
      _pendingRequests[requestId] = taskSource;

      try {
        _tradingWebSocket.Send(requestJson);

        using (var cts = new CancellationTokenSource(timeoutMs)) {
          cts.Token.Register(() => taskSource.TrySetException(new TimeoutException("WebSocket request timeout")));
          return await taskSource.Task;
        }
      } catch (Exception) {
        _pendingRequests.TryRemove(requestId, out _);
        throw;
      }
    }

    private async Task<bool> PlaceOrderWebSocketAsync(QuantConnect.Orders.Order order) {
      try {
        Log.Trace($"BinanceWebSocketTradingApi.PlaceOrderWebSocketAsync(): Placing order {order.Id} via WebSocket API");

        var parameters = CreateWebSocketOrderParameters(order);
        var response = await SendWebSocketRequestAsync("order.place", parameters);

        var status = response["status"]?.ToObject<int>();
        if (status == 200) {
          var result = response["result"];
          if (result != null) {

            var orderResponse = ConvertWebSocketOrderResponse(result, order);
            OnOrderSubmit(orderResponse, order);

            Log.Trace($"BinanceWebSocketTradingApi.PlaceOrderWebSocketAsync(): Successfully placed order {order.Id} via WebSocket");
            return true;
          }
        }

        var errorMsg = $"WebSocket order placement failed. Status: {status}, Response: {response}";
        Log.Error($"BinanceWebSocketTradingApi.PlaceOrderWebSocketAsync(): {errorMsg}");

        OnOrderEvent(new OrderEvent(order, DateTime.UtcNow, OrderFee.Zero, "Binance WebSocket Order Event") {
          Status = OrderStatus.Invalid,
          Message = errorMsg
        });

        return false;
      } catch (Exception ex) {
        Log.Error($"BinanceWebSocketTradingApi.PlaceOrderWebSocketAsync(): Exception placing order {order.Id}: {ex.Message}");

        OnOrderEvent(new OrderEvent(order, DateTime.UtcNow, OrderFee.Zero, "Binance WebSocket Order Event") {
          Status = OrderStatus.Invalid,
          Message = ex.Message
        });

        return false;
      }
    }

    private async Task<bool> CancelOrderWebSocketAsync(QuantConnect.Orders.Order order) {
      try {
        Log.Trace($"BinanceWebSocketTradingApi.CancelOrderWebSocketAsync(): Cancelling order {order.Id} via WebSocket API");

        var allSuccess = true;
        foreach (var brokerId in order.BrokerId) {
          var parameters = new Dictionary<string, object> {
            ["symbol"] = SymbolMapper.GetBrokerageSymbol(order.Symbol),
            ["orderId"] = brokerId
          };

          var response = await SendWebSocketRequestAsync("order.cancel", parameters);
          var status = response["status"]?.ToObject<int>();

          if (status != 200) {
            allSuccess = false;
            Log.Error($"BinanceWebSocketTradingApi.CancelOrderWebSocketAsync(): Failed to cancel order {order.Id}, broker ID {brokerId}. Status: {status}");
          } else {
            Log.Trace($"BinanceWebSocketTradingApi.CancelOrderWebSocketAsync(): Successfully cancelled order {order.Id}, broker ID {brokerId} via WebSocket");
          }
        }

        if (allSuccess) {
          OnOrderEvent(new OrderEvent(order, DateTime.UtcNow, OrderFee.Zero, "Binance WebSocket Order Event") {
            Status = OrderStatus.Canceled
          });
        }

        return allSuccess;
      } catch (Exception ex) {
        Log.Error($"BinanceWebSocketTradingApi.CancelOrderWebSocketAsync(): Exception cancelling order {order.Id}: {ex.Message}");
        return false;
      }
    }

    private Dictionary<string, object> CreateWebSocketOrderParameters(QuantConnect.Orders.Order order) {
      var parameters = new Dictionary<string, object> {
        ["symbol"] = SymbolMapper.GetBrokerageSymbol(order.Symbol),
        ["side"] = ConvertOrderDirection(order.Direction),
        ["quantity"] = Math.Abs(order.Quantity).ToString(CultureInfo.InvariantCulture)
      };

      parameters["positionSide"] = "BOTH";

      switch (order) {
        case LimitOrder limitOrder:
          parameters["type"] = "LIMIT";
          parameters["timeInForce"] = "GTX";
          parameters["price"] = limitOrder.LimitPrice.ToString(CultureInfo.InvariantCulture);
          break;

        case StopLimitOrder stopLimitOrder:
          var tickerPrice = GetTickerPrice(order);
          var stopPrice = stopLimitOrder.StopPrice;
          parameters["type"] = (order.Direction == OrderDirection.Sell) == (stopPrice <= tickerPrice) ? "STOP" : "TAKE_PROFIT";

          parameters["timeInForce"] = "GTX";
          parameters["stopPrice"] = stopPrice.ToString(CultureInfo.InvariantCulture);
          parameters["price"] = stopLimitOrder.LimitPrice.ToString(CultureInfo.InvariantCulture);
          break;

        case MarketOrder:
          parameters["type"] = "MARKET";
          break;

        case StopMarketOrder stopMarketOrder:
          parameters["type"] = "STOP_MARKET";
          parameters["stopPrice"] = stopMarketOrder.StopPrice.ToString(CultureInfo.InvariantCulture);
          break;

        default:
          throw new NotSupportedException($"BinanceWebSocketTradingApi.CreateWebSocketOrderParameters(): Unsupported order type: {order.Type}");
      }

      Log.Trace($"BinanceWebSocketTradingApi.CreateWebSocketOrderParameters(): Created parameters for {order.Type} order {order.Id}");
      return parameters;
    }

    private NewOrder ConvertWebSocketOrderResponse(JToken result, QuantConnect.Orders.Order order) {
      return new NewOrder {
        Id = result["orderId"]?.ToString(),
        Symbol = result["symbol"]?.ToString(),
        Status = result["status"]?.ToString(),
        Type = result["type"]?.ToString(),
        Side = result["side"]?.ToString(),
        OriginalAmount = result["origQty"]?.ToObject<decimal>() ?? 0,
        ExecutedAmount = result["executedQty"]?.ToObject<decimal>() ?? 0,
        Price = result["price"]?.ToObject<decimal>() ?? 0,
        StopPrice = result["stopPrice"]?.ToObject<decimal>() ?? 0,
        TransactionTime = result["updateTime"]?.ToObject<long>() ?? DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
      };
    }

    public override bool PlaceOrder(QuantConnect.Orders.Order order) {
      if (UseWebSocketApiForTrading) {
        Log.Trace($"BinanceWebSocketTradingApi.PlaceOrder(): Using WebSocket API for order {order.Id}");

        try {
          var task = PlaceOrderWebSocketAsync(order);
          if (task.Wait(DefaultWebSocketTimeoutMs)) {
            return task.Result;
          } else {
            Log.Error($"BinanceWebSocketTradingApi.PlaceOrder(): WebSocket order placement timed out for order {order.Id}");
            return base.PlaceOrder(order);
          }
        } catch (Exception ex) {
          Log.Error($"BinanceWebSocketTradingApi.PlaceOrder(): WebSocket order placement failed for order {order.Id}, falling back to REST API: {ex.Message}");

          return base.PlaceOrder(order);
        }
      } else {
        Log.Trace($"BinanceWebSocketTradingApi.PlaceOrder(): Using REST API for order {order.Id}");
        return base.PlaceOrder(order);
      }
    }

    public override bool CancelOrder(QuantConnect.Orders.Order order) {
      if (UseWebSocketApiForTrading) {
        Log.Trace($"BinanceWebSocketTradingApi.CancelOrder(): Using WebSocket API for order {order.Id}");

        try {
          var task = CancelOrderWebSocketAsync(order);
          if (task.Wait(DefaultWebSocketTimeoutMs)) {
            return task.Result;
          } else {
            Log.Error($"BinanceWebSocketTradingApi.CancelOrder(): WebSocket order cancellation timed out for order {order.Id}");
            return base.CancelOrder(order);
          }
        } catch (Exception ex) {
          Log.Error($"BinanceWebSocketTradingApi.CancelOrder(): WebSocket order cancellation failed for order {order.Id}, falling back to REST API: {ex.Message}");

          return base.CancelOrder(order);
        }
      } else {
        Log.Trace($"BinanceWebSocketTradingApi.CancelOrder(): Using REST API for order {order.Id}");
        return base.CancelOrder(order);
      }
    }
  }
}

﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NodaTime" Version="3.2.2" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\..\Lean\Algorithm.CSharp\QuantConnect.Algorithm.CSharp.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\Algorithm.Framework\QuantConnect.Algorithm.Framework.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\AlgorithmFactory\QuantConnect.AlgorithmFactory.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\Algorithm\QuantConnect.Algorithm.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\Api\QuantConnect.Api.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\Brokerages\QuantConnect.Brokerages.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\Common\QuantConnect.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\Compression\QuantConnect.Compression.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\Configuration\QuantConnect.Configuration.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\Engine\QuantConnect.Lean.Engine.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\Indicators\QuantConnect.Indicators.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\Logging\QuantConnect.Logging.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\Messaging\QuantConnect.Messaging.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Lean\Queues\QuantConnect.Queues.csproj" />
  </ItemGroup>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy /Y &quot;$(TargetPath)&quot; &quot;D:\work\Lean\Launcher\bin\$(Configuration)\&quot;&#xD;&#xA;&#xD;&#xA;del /q &quot;D:\work\xstarwalker168\Python\Finance\QuantConnectLean\QC-Log-Dir\*&quot;&#xD;&#xA;" />
  </Target>

</Project>